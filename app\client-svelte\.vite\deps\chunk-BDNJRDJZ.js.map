{"version": 3, "sources": ["../../node_modules/svelte/src/internal/shared/utils.js", "../../node_modules/svelte/src/internal/client/constants.js", "../../node_modules/svelte/src/internal/shared/errors.js", "../../node_modules/svelte/src/internal/client/errors.js", "../../node_modules/svelte/src/constants.js", "../../node_modules/svelte/src/internal/client/context.js", "../../node_modules/svelte/src/internal/client/dom/task.js", "../../node_modules/svelte/src/internal/client/error-handling.js", "../../node_modules/svelte/src/internal/client/reactivity/equality.js", "../../node_modules/svelte/src/internal/shared/warnings.js", "../../node_modules/svelte/src/internal/shared/clone.js", "../../node_modules/svelte/src/internal/client/dev/tracing.js", "../../node_modules/svelte/src/reactivity/create-subscriber.js", "../../node_modules/svelte/src/internal/client/dom/blocks/boundary.js", "../../node_modules/svelte/src/internal/client/dom/blocks/each.js", "../../node_modules/svelte/src/internal/client/reactivity/async.js", "../../node_modules/svelte/src/internal/client/reactivity/deriveds.js", "../../node_modules/svelte/src/internal/client/reactivity/sources.js", "../../node_modules/svelte/src/internal/client/reactivity/batch.js", "../../node_modules/svelte/src/internal/client/dom/elements/misc.js", "../../node_modules/svelte/src/internal/client/dom/elements/bindings/shared.js", "../../node_modules/svelte/src/internal/client/reactivity/effects.js", "../../node_modules/svelte/src/internal/client/legacy.js", "../../node_modules/svelte/src/internal/client/runtime.js", "../../node_modules/svelte/src/internal/client/proxy.js", "../../node_modules/svelte/src/internal/client/dev/equality.js", "../../node_modules/svelte/src/internal/client/dom/operations.js", "../../node_modules/svelte/src/internal/client/dom/hydration.js", "../../node_modules/svelte/src/internal/client/dom/elements/events.js"], "sourcesContent": ["// Store the references to globals in case someone tries to monkey patch these, causing the below\n// to de-opt (this occurs often when using popular extensions).\nexport var is_array = Array.isArray;\nexport var index_of = Array.prototype.indexOf;\nexport var array_from = Array.from;\nexport var object_keys = Object.keys;\nexport var define_property = Object.defineProperty;\nexport var get_descriptor = Object.getOwnPropertyDescriptor;\nexport var get_descriptors = Object.getOwnPropertyDescriptors;\nexport var object_prototype = Object.prototype;\nexport var array_prototype = Array.prototype;\nexport var get_prototype_of = Object.getPrototypeOf;\nexport var is_extensible = Object.isExtensible;\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\nexport const noop = () => {};\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n\n/**\n * @template [T=any]\n * @param {any} value\n * @returns {value is PromiseLike<T>}\n */\nexport function is_promise(value) {\n\treturn typeof value?.then === 'function';\n}\n\n/** @param {Function} fn */\nexport function run(fn) {\n\treturn fn();\n}\n\n/** @param {Array<() => void>} arr */\nexport function run_all(arr) {\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tarr[i]();\n\t}\n}\n\n/**\n * TODO replace with Promise.withResolvers once supported widely enough\n * @template T\n */\nexport function deferred() {\n\t/** @type {(value: T) => void} */\n\tvar resolve;\n\n\t/** @type {(reason: any) => void} */\n\tvar reject;\n\n\t/** @type {Promise<T>} */\n\tvar promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\n\t// @ts-expect-error\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template V\n * @param {V} value\n * @param {V | (() => V)} fallback\n * @param {boolean} [lazy]\n * @returns {V}\n */\nexport function fallback(value, fallback, lazy = false) {\n\treturn value === undefined\n\t\t? lazy\n\t\t\t? /** @type {() => V} */ (fallback)()\n\t\t\t: /** @type {V} */ (fallback)\n\t\t: value;\n}\n\n/**\n * When encountering a situation like `let [a, b, c] = $derived(blah())`,\n * we need to stash an intermediate value that `a`, `b`, and `c` derive\n * from, in case it's an iterable\n * @template T\n * @param {ArrayLike<T> | Iterable<T>} value\n * @param {number} [n]\n * @returns {Array<T>}\n */\nexport function to_array(value, n) {\n\t// return arrays unchanged\n\tif (Array.isArray(value)) {\n\t\treturn value;\n\t}\n\n\t// if value is not iterable, or `n` is unspecified (indicates a rest\n\t// element, which means we're not concerned about unbounded iterables)\n\t// convert to an array with `Array.from`\n\tif (n === undefined || !(Symbol.iterator in value)) {\n\t\treturn Array.from(value);\n\t}\n\n\t// otherwise, populate an array with `n` values\n\n\t/** @type {T[]} */\n\tconst array = [];\n\n\tfor (const element of value) {\n\t\tarray.push(element);\n\t\tif (array.length === n) break;\n\t}\n\n\treturn array;\n}\n", "// General flags\nexport const DERIVED = 1 << 1;\nexport const EFFECT = 1 << 2;\nexport const RENDER_EFFECT = 1 << 3;\nexport const BLOCK_EFFECT = 1 << 4;\nexport const BRANCH_EFFECT = 1 << 5;\nexport const ROOT_EFFECT = 1 << 6;\nexport const BOUNDARY_EFFECT = 1 << 7;\n/**\n * Indicates that a reaction is connected to an effect root — either it is an effect,\n * or it is a derived that is depended on by at least one effect. If a derived has\n * no dependents, we can disconnect it from the graph, allowing it to either be\n * GC'd or reconnected later if an effect comes to depend on it again\n */\nexport const CONNECTED = 1 << 9;\nexport const CLEAN = 1 << 10;\nexport const DIRTY = 1 << 11;\nexport const MAYBE_DIRTY = 1 << 12;\nexport const INERT = 1 << 13;\nexport const DESTROYED = 1 << 14;\n\n// Flags exclusive to effects\n/** Set once an effect that should run synchronously has run */\nexport const EFFECT_RAN = 1 << 15;\n/**\n * 'Transparent' effects do not create a transition boundary.\n * This is on a block effect 99% of the time but may also be on a branch effect if its parent block effect was pruned\n */\nexport const EFFECT_TRANSPARENT = 1 << 16;\nexport const EAGER_EFFECT = 1 << 17;\nexport const HEAD_EFFECT = 1 << 18;\nexport const EFFECT_PRESERVED = 1 << 19;\nexport const USER_EFFECT = 1 << 20;\n\n// Flags exclusive to deriveds\n/**\n * Tells that we marked this derived and its reactions as visited during the \"mark as (maybe) dirty\"-phase.\n * Will be lifted during execution of the derived and during checking its dirty state (both are necessary\n * because a derived might be checked but not executed).\n */\nexport const WAS_MARKED = 1 << 15;\n\n// Flags used for async\nexport const REACTION_IS_UPDATING = 1 << 21;\nexport const ASYNC = 1 << 22;\n\nexport const ERROR_VALUE = 1 << 23;\n\nexport const STATE_SYMBOL = Symbol('$state');\nexport const LEGACY_PROPS = Symbol('legacy props');\nexport const LOADING_ATTR_SYMBOL = Symbol('');\nexport const PROXY_PATH_SYMBOL = Symbol('proxy path');\n\n/** allow users to ignore aborted signal errors if `reason.name === 'StaleReactionError` */\nexport const STALE_REACTION = new (class StaleReactionError extends Error {\n\tname = 'StaleReactionError';\n\tmessage = 'The reaction that called `getAbortSignal()` was re-run or destroyed';\n})();\n\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 3;\nexport const COMMENT_NODE = 8;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Cannot use `{@render children(...)}` if the parent component uses `let:` directives. Consider using a named snippet instead\n * @returns {never}\n */\nexport function invalid_default_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_default_snippet\\nCannot use \\`{@render children(...)}\\` if the parent component uses \\`let:\\` directives. Consider using a named snippet instead\\nhttps://svelte.dev/e/invalid_default_snippet`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_default_snippet`);\n\t}\n}\n\n/**\n * A snippet function was passed invalid arguments. Snippets should only be instantiated via `{@render ...}`\n * @returns {never}\n */\nexport function invalid_snippet_arguments() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet_arguments\\nA snippet function was passed invalid arguments. Snippets should only be instantiated via \\`{@render ...}\\`\\nhttps://svelte.dev/e/invalid_snippet_arguments`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet_arguments`);\n\t}\n}\n\n/**\n * `%name%(...)` can only be used during component initialisation\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_outside_component(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_outside_component\\n\\`${name}(...)\\` can only be used during component initialisation\\nhttps://svelte.dev/e/lifecycle_outside_component`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_outside_component`);\n\t}\n}\n\n/**\n * Context was not set in a parent component\n * @returns {never}\n */\nexport function missing_context() {\n\tif (DEV) {\n\t\tconst error = new Error(`missing_context\\nContext was not set in a parent component\\nhttps://svelte.dev/e/missing_context`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/missing_context`);\n\t}\n}\n\n/**\n * Attempted to render a snippet without a `{@render}` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change `{snippet}` to `{@render snippet()}`.\n * @returns {never}\n */\nexport function snippet_without_render_tag() {\n\tif (DEV) {\n\t\tconst error = new Error(`snippet_without_render_tag\\nAttempted to render a snippet without a \\`{@render}\\` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change \\`{snippet}\\` to \\`{@render snippet()}\\`.\\nhttps://svelte.dev/e/snippet_without_render_tag`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/snippet_without_render_tag`);\n\t}\n}\n\n/**\n * `%name%` is not a store with a `subscribe` method\n * @param {string} name\n * @returns {never}\n */\nexport function store_invalid_shape(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`store_invalid_shape\\n\\`${name}\\` is not a store with a \\`subscribe\\` method\\nhttps://svelte.dev/e/store_invalid_shape`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/store_invalid_shape`);\n\t}\n}\n\n/**\n * The `this` prop on `<svelte:element>` must be a string, if defined\n * @returns {never}\n */\nexport function svelte_element_invalid_this_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`svelte_element_invalid_this_value\\nThe \\`this\\` prop on \\`<svelte:element>\\` must be a string, if defined\\nhttps://svelte.dev/e/svelte_element_invalid_this_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/svelte_element_invalid_this_value`);\n\t}\n}", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nexport *  from '../shared/errors.js';\n\n/**\n * Cannot create a `$derived(...)` with an `await` expression outside of an effect tree\n * @returns {never}\n */\nexport function async_derived_orphan() {\n\tif (DEV) {\n\t\tconst error = new Error(`async_derived_orphan\\nCannot create a \\`$derived(...)\\` with an \\`await\\` expression outside of an effect tree\\nhttps://svelte.dev/e/async_derived_orphan`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/async_derived_orphan`);\n\t}\n}\n\n/**\n * Using `bind:value` together with a checkbox input is not allowed. Use `bind:checked` instead\n * @returns {never}\n */\nexport function bind_invalid_checkbox_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_checkbox_value\\nUsing \\`bind:value\\` together with a checkbox input is not allowed. Use \\`bind:checked\\` instead\\nhttps://svelte.dev/e/bind_invalid_checkbox_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_checkbox_value`);\n\t}\n}\n\n/**\n * Component %component% has an export named `%key%` that a consumer component is trying to access using `bind:%key%`, which is disallowed. Instead, use `bind:this` (e.g. `<%name% bind:this={component} />`) and then access the property on the bound component instance (e.g. `component.%key%`)\n * @param {string} component\n * @param {string} key\n * @param {string} name\n * @returns {never}\n */\nexport function bind_invalid_export(component, key, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_export\\nComponent ${component} has an export named \\`${key}\\` that a consumer component is trying to access using \\`bind:${key}\\`, which is disallowed. Instead, use \\`bind:this\\` (e.g. \\`<${name} bind:this={component} />\\`) and then access the property on the bound component instance (e.g. \\`component.${key}\\`)\\nhttps://svelte.dev/e/bind_invalid_export`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_export`);\n\t}\n}\n\n/**\n * A component is attempting to bind to a non-bindable property `%key%` belonging to %component% (i.e. `<%name% bind:%key%={...}>`). To mark a property as bindable: `let { %key% = $bindable() } = $props()`\n * @param {string} key\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function bind_not_bindable(key, component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_not_bindable\\nA component is attempting to bind to a non-bindable property \\`${key}\\` belonging to ${component} (i.e. \\`<${name} bind:${key}={...}>\\`). To mark a property as bindable: \\`let { ${key} = $bindable() } = $props()\\`\\nhttps://svelte.dev/e/bind_not_bindable`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_not_bindable`);\n\t}\n}\n\n/**\n * Calling `%method%` on a component instance (of %component%) is no longer valid in Svelte 5\n * @param {string} method\n * @param {string} component\n * @returns {never}\n */\nexport function component_api_changed(method, component) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_changed\\nCalling \\`${method}\\` on a component instance (of ${component}) is no longer valid in Svelte 5\\nhttps://svelte.dev/e/component_api_changed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_changed`);\n\t}\n}\n\n/**\n * Attempted to instantiate %component% with `new %name%`, which is no longer valid in Svelte 5. If this component is not under your control, set the `compatibility.componentApi` compiler option to `4` to keep it working.\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function component_api_invalid_new(component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_invalid_new\\nAttempted to instantiate ${component} with \\`new ${name}\\`, which is no longer valid in Svelte 5. If this component is not under your control, set the \\`compatibility.componentApi\\` compiler option to \\`4\\` to keep it working.\\nhttps://svelte.dev/e/component_api_invalid_new`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_invalid_new`);\n\t}\n}\n\n/**\n * A derived value cannot reference itself recursively\n * @returns {never}\n */\nexport function derived_references_self() {\n\tif (DEV) {\n\t\tconst error = new Error(`derived_references_self\\nA derived value cannot reference itself recursively\\nhttps://svelte.dev/e/derived_references_self`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/derived_references_self`);\n\t}\n}\n\n/**\n * Keyed each block has duplicate key `%value%` at indexes %a% and %b%\n * @param {string} a\n * @param {string} b\n * @param {string | undefined | null} [value]\n * @returns {never}\n */\nexport function each_key_duplicate(a, b, value) {\n\tif (DEV) {\n\t\tconst error = new Error(`each_key_duplicate\\n${value\n\t\t\t? `Keyed each block has duplicate key \\`${value}\\` at indexes ${a} and ${b}`\n\t\t\t: `Keyed each block has duplicate key at indexes ${a} and ${b}`}\\nhttps://svelte.dev/e/each_key_duplicate`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/each_key_duplicate`);\n\t}\n}\n\n/**\n * `%rune%` cannot be used inside an effect cleanup function\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_in_teardown(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_teardown\\n\\`${rune}\\` cannot be used inside an effect cleanup function\\nhttps://svelte.dev/e/effect_in_teardown`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_teardown`);\n\t}\n}\n\n/**\n * Effect cannot be created inside a `$derived` value that was not itself created inside an effect\n * @returns {never}\n */\nexport function effect_in_unowned_derived() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_unowned_derived\\nEffect cannot be created inside a \\`$derived\\` value that was not itself created inside an effect\\nhttps://svelte.dev/e/effect_in_unowned_derived`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_unowned_derived`);\n\t}\n}\n\n/**\n * `%rune%` can only be used inside an effect (e.g. during component initialisation)\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_orphan(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_orphan\\n\\`${rune}\\` can only be used inside an effect (e.g. during component initialisation)\\nhttps://svelte.dev/e/effect_orphan`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_orphan`);\n\t}\n}\n\n/**\n * `$effect.pending()` can only be called inside an effect or derived\n * @returns {never}\n */\nexport function effect_pending_outside_reaction() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_pending_outside_reaction\\n\\`$effect.pending()\\` can only be called inside an effect or derived\\nhttps://svelte.dev/e/effect_pending_outside_reaction`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_pending_outside_reaction`);\n\t}\n}\n\n/**\n * Maximum update depth exceeded. This typically indicates that an effect reads and writes the same piece of state\n * @returns {never}\n */\nexport function effect_update_depth_exceeded() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_update_depth_exceeded\\nMaximum update depth exceeded. This typically indicates that an effect reads and writes the same piece of state\\nhttps://svelte.dev/e/effect_update_depth_exceeded`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_update_depth_exceeded`);\n\t}\n}\n\n/**\n * Cannot use `fork(...)` unless the `experimental.async` compiler option is `true`\n * @returns {never}\n */\nexport function experimental_async_fork() {\n\tif (DEV) {\n\t\tconst error = new Error(`experimental_async_fork\\nCannot use \\`fork(...)\\` unless the \\`experimental.async\\` compiler option is \\`true\\`\\nhttps://svelte.dev/e/experimental_async_fork`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/experimental_async_fork`);\n\t}\n}\n\n/**\n * Cannot use `flushSync` inside an effect\n * @returns {never}\n */\nexport function flush_sync_in_effect() {\n\tif (DEV) {\n\t\tconst error = new Error(`flush_sync_in_effect\\nCannot use \\`flushSync\\` inside an effect\\nhttps://svelte.dev/e/flush_sync_in_effect`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/flush_sync_in_effect`);\n\t}\n}\n\n/**\n * Cannot commit a fork that was already discarded\n * @returns {never}\n */\nexport function fork_discarded() {\n\tif (DEV) {\n\t\tconst error = new Error(`fork_discarded\\nCannot commit a fork that was already discarded\\nhttps://svelte.dev/e/fork_discarded`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/fork_discarded`);\n\t}\n}\n\n/**\n * Cannot create a fork inside an effect or when state changes are pending\n * @returns {never}\n */\nexport function fork_timing() {\n\tif (DEV) {\n\t\tconst error = new Error(`fork_timing\\nCannot create a fork inside an effect or when state changes are pending\\nhttps://svelte.dev/e/fork_timing`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/fork_timing`);\n\t}\n}\n\n/**\n * `getAbortSignal()` can only be called inside an effect or derived\n * @returns {never}\n */\nexport function get_abort_signal_outside_reaction() {\n\tif (DEV) {\n\t\tconst error = new Error(`get_abort_signal_outside_reaction\\n\\`getAbortSignal()\\` can only be called inside an effect or derived\\nhttps://svelte.dev/e/get_abort_signal_outside_reaction`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/get_abort_signal_outside_reaction`);\n\t}\n}\n\n/**\n * Failed to hydrate the application\n * @returns {never}\n */\nexport function hydration_failed() {\n\tif (DEV) {\n\t\tconst error = new Error(`hydration_failed\\nFailed to hydrate the application\\nhttps://svelte.dev/e/hydration_failed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/hydration_failed`);\n\t}\n}\n\n/**\n * Could not `{@render}` snippet due to the expression being `null` or `undefined`. Consider using optional chaining `{@render snippet?.()}`\n * @returns {never}\n */\nexport function invalid_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet\\nCould not \\`{@render}\\` snippet due to the expression being \\`null\\` or \\`undefined\\`. Consider using optional chaining \\`{@render snippet?.()}\\`\\nhttps://svelte.dev/e/invalid_snippet`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet`);\n\t}\n}\n\n/**\n * `%name%(...)` cannot be used in runes mode\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_legacy_only(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_legacy_only\\n\\`${name}(...)\\` cannot be used in runes mode\\nhttps://svelte.dev/e/lifecycle_legacy_only`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_legacy_only`);\n\t}\n}\n\n/**\n * Cannot do `bind:%key%={undefined}` when `%key%` has a fallback value\n * @param {string} key\n * @returns {never}\n */\nexport function props_invalid_value(key) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_invalid_value\\nCannot do \\`bind:${key}={undefined}\\` when \\`${key}\\` has a fallback value\\nhttps://svelte.dev/e/props_invalid_value`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_invalid_value`);\n\t}\n}\n\n/**\n * Rest element properties of `$props()` such as `%property%` are readonly\n * @param {string} property\n * @returns {never}\n */\nexport function props_rest_readonly(property) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_rest_readonly\\nRest element properties of \\`$props()\\` such as \\`${property}\\` are readonly\\nhttps://svelte.dev/e/props_rest_readonly`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_rest_readonly`);\n\t}\n}\n\n/**\n * The `%rune%` rune is only available inside `.svelte` and `.svelte.js/ts` files\n * @param {string} rune\n * @returns {never}\n */\nexport function rune_outside_svelte(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`rune_outside_svelte\\nThe \\`${rune}\\` rune is only available inside \\`.svelte\\` and \\`.svelte.js/ts\\` files\\nhttps://svelte.dev/e/rune_outside_svelte`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/rune_outside_svelte`);\n\t}\n}\n\n/**\n * `setContext` must be called when a component first initializes, not in a subsequent effect or after an `await` expression\n * @returns {never}\n */\nexport function set_context_after_init() {\n\tif (DEV) {\n\t\tconst error = new Error(`set_context_after_init\\n\\`setContext\\` must be called when a component first initializes, not in a subsequent effect or after an \\`await\\` expression\\nhttps://svelte.dev/e/set_context_after_init`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/set_context_after_init`);\n\t}\n}\n\n/**\n * Property descriptors defined on `$state` objects must contain `value` and always be `enumerable`, `configurable` and `writable`.\n * @returns {never}\n */\nexport function state_descriptors_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_descriptors_fixed\\nProperty descriptors defined on \\`$state\\` objects must contain \\`value\\` and always be \\`enumerable\\`, \\`configurable\\` and \\`writable\\`.\\nhttps://svelte.dev/e/state_descriptors_fixed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_descriptors_fixed`);\n\t}\n}\n\n/**\n * Cannot set prototype of `$state` object\n * @returns {never}\n */\nexport function state_prototype_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_prototype_fixed\\nCannot set prototype of \\`$state\\` object\\nhttps://svelte.dev/e/state_prototype_fixed`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_prototype_fixed`);\n\t}\n}\n\n/**\n * Updating state inside `$derived(...)`, `$inspect(...)` or a template expression is forbidden. If the value should not be reactive, declare it without `$state`\n * @returns {never}\n */\nexport function state_unsafe_mutation() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_unsafe_mutation\\nUpdating state inside \\`$derived(...)\\`, \\`$inspect(...)\\` or a template expression is forbidden. If the value should not be reactive, declare it without \\`$state\\`\\nhttps://svelte.dev/e/state_unsafe_mutation`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_unsafe_mutation`);\n\t}\n}\n\n/**\n * A `<svelte:boundary>` `reset` function cannot be called while an error is still being handled\n * @returns {never}\n */\nexport function svelte_boundary_reset_onerror() {\n\tif (DEV) {\n\t\tconst error = new Error(`svelte_boundary_reset_onerror\\nA \\`<svelte:boundary>\\` \\`reset\\` function cannot be called while an error is still being handled\\nhttps://svelte.dev/e/svelte_boundary_reset_onerror`);\n\n\t\terror.name = 'Svelte error';\n\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/svelte_boundary_reset_onerror`);\n\t}\n}", "export const EACH_ITEM_REACTIVE = 1;\nexport const EACH_INDEX_REACTIVE = 1 << 1;\n/** See EachBlock interface metadata.is_controlled for an explanation what this is */\nexport const EACH_IS_CONTROLLED = 1 << 2;\nexport const EACH_IS_ANIMATED = 1 << 3;\nexport const EACH_ITEM_IMMUTABLE = 1 << 4;\n\nexport const PROPS_IS_IMMUTABLE = 1;\nexport const PROPS_IS_RUNES = 1 << 1;\nexport const PROPS_IS_UPDATED = 1 << 2;\nexport const PROPS_IS_BINDABLE = 1 << 3;\nexport const PROPS_IS_LAZY_INITIAL = 1 << 4;\n\nexport const TRANSITION_IN = 1;\nexport const TRANSITION_OUT = 1 << 1;\nexport const TRANSITION_GLOBAL = 1 << 2;\n\nexport const TEMPLATE_FRAGMENT = 1;\nexport const TEMPLATE_USE_IMPORT_NODE = 1 << 1;\nexport const TEMPLATE_USE_SVG = 1 << 2;\nexport const TEMPLATE_USE_MATHML = 1 << 3;\n\nexport const HYDRATION_START = '[';\n/** used to indicate that an `{:else}...` block was rendered */\nexport const HYDRATION_START_ELSE = '[!';\nexport const HYDRATION_END = ']';\nexport const HYDRATION_ERROR = {};\n\nexport const ELEMENT_IS_NAMESPACED = 1;\nexport const ELEMENT_PRESERVE_ATTRIBUTE_CASE = 1 << 1;\nexport const ELEMENT_IS_INPUT = 1 << 2;\n\nexport const UNINITIALIZED = Symbol();\n\n// Dev-time component properties\nexport const FILENAME = Symbol('filename');\nexport const HMR = Symbol('hmr');\n\nexport const NAMESPACE_HTML = 'http://www.w3.org/1999/xhtml';\nexport const NAMESPACE_SVG = 'http://www.w3.org/2000/svg';\nexport const NAMESPACE_MATHML = 'http://www.w3.org/1998/Math/MathML';\n\n// we use a list of ignorable runtime warnings because not every runtime warning\n// can be ignored and we want to keep the validation for svelte-ignore in place\nexport const IGNORABLE_RUNTIME_WARNINGS = /** @type {const} */ ([\n\t'await_waterfall',\n\t'await_reactivity_loss',\n\t'state_snapshot_uncloneable',\n\t'binding_property_non_reactive',\n\t'hydration_attribute_changed',\n\t'hydration_html_changed',\n\t'ownership_invalid_binding',\n\t'ownership_invalid_mutation'\n]);\n\n/**\n * Whitespace inside one of these elements will not result in\n * a whitespace node being created in any circumstances. (This\n * list is almost certainly very incomplete)\n * TODO this is currently unused\n */\nexport const ELEMENTS_WITHOUT_TEXT = ['audio', 'datalist', 'dl', 'optgroup', 'select', 'video'];\n\nexport const ATTACHMENT_KEY = '@attach';\n", "/** @import { ComponentContext, DevStackEntry, Effect } from '#client' */\nimport { DEV } from 'esm-env';\nimport * as e from './errors.js';\nimport { active_effect, active_reaction } from './runtime.js';\nimport { create_user_effect } from './reactivity/effects.js';\nimport { async_mode_flag, legacy_mode_flag } from '../flags/index.js';\nimport { FILENAME } from '../../constants.js';\nimport { BRANCH_EFFECT, EFFECT_RAN } from './constants.js';\n\n/** @type {ComponentContext | null} */\nexport let component_context = null;\n\n/** @param {ComponentContext | null} context */\nexport function set_component_context(context) {\n\tcomponent_context = context;\n}\n\n/** @type {DevStackEntry | null} */\nexport let dev_stack = null;\n\n/** @param {DevStackEntry | null} stack */\nexport function set_dev_stack(stack) {\n\tdev_stack = stack;\n}\n\n/**\n * Execute a callback with a new dev stack entry\n * @param {() => any} callback - Function to execute\n * @param {DevStackEntry['type']} type - Type of block/component\n * @param {any} component - Component function\n * @param {number} line - Line number\n * @param {number} column - Column number\n * @param {Record<string, any>} [additional] - Any additional properties to add to the dev stack entry\n * @returns {any}\n */\nexport function add_svelte_meta(callback, type, component, line, column, additional) {\n\tconst parent = dev_stack;\n\n\tdev_stack = {\n\t\ttype,\n\t\tfile: component[FILENAME],\n\t\tline,\n\t\tcolumn,\n\t\tparent,\n\t\t...additional\n\t};\n\n\ttry {\n\t\treturn callback();\n\t} finally {\n\t\tdev_stack = parent;\n\t}\n}\n\n/**\n * The current component function. Different from current component context:\n * ```html\n * <!-- App.svelte -->\n * <Foo>\n *   <Bar /> <!-- context == Foo.svelte, function == App.svelte -->\n * </Foo>\n * ```\n * @type {ComponentContext['function']}\n */\nexport let dev_current_component_function = null;\n\n/** @param {ComponentContext['function']} fn */\nexport function set_dev_current_component_function(fn) {\n\tdev_current_component_function = fn;\n}\n\n/**\n * Returns a `[get, set]` pair of functions for working with context in a type-safe way.\n *\n * `get` will throw an error if no parent component called `set`.\n *\n * @template T\n * @returns {[() => T, (context: T) => T]}\n * @since 5.40.0\n */\nexport function createContext() {\n\tconst key = {};\n\n\treturn [\n\t\t() => {\n\t\t\tif (!hasContext(key)) {\n\t\t\t\te.missing_context();\n\t\t\t}\n\n\t\t\treturn getContext(key);\n\t\t},\n\t\t(context) => setContext(key, context)\n\t];\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * [`createContext`](https://svelte.dev/docs/svelte/svelte#createContext) is a type-safe alternative.\n *\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\tconst context_map = get_or_init_context_map('getContext');\n\tconst result = /** @type {T} */ (context_map.get(key));\n\treturn result;\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * [`createContext`](https://svelte.dev/docs/svelte/svelte#createContext) is a type-safe alternative.\n *\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tconst context_map = get_or_init_context_map('setContext');\n\n\tif (async_mode_flag) {\n\t\tvar flags = /** @type {Effect} */ (active_effect).f;\n\t\tvar valid =\n\t\t\t!active_reaction &&\n\t\t\t(flags & BRANCH_EFFECT) !== 0 &&\n\t\t\t// pop() runs synchronously, so this indicates we're setting context after an await\n\t\t\t!(/** @type {ComponentContext} */ (component_context).i);\n\n\t\tif (!valid) {\n\t\t\te.set_context_after_init();\n\t\t}\n\t}\n\n\tcontext_map.set(key, context);\n\treturn context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\tconst context_map = get_or_init_context_map('hasContext');\n\treturn context_map.has(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\tconst context_map = get_or_init_context_map('getAllContexts');\n\treturn /** @type {T} */ (context_map);\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {any} runes\n * @param {Function} [fn]\n * @returns {void}\n */\nexport function push(props, runes = false, fn) {\n\tcomponent_context = {\n\t\tp: component_context,\n\t\ti: false,\n\t\tc: null,\n\t\te: null,\n\t\ts: props,\n\t\tx: null,\n\t\tl: legacy_mode_flag && !runes ? { s: null, u: null, $: [] } : null\n\t};\n\n\tif (DEV) {\n\t\t// component function\n\t\tcomponent_context.function = fn;\n\t\tdev_current_component_function = fn;\n\t}\n}\n\n/**\n * @template {Record<string, any>} T\n * @param {T} [component]\n * @returns {T}\n */\nexport function pop(component) {\n\tvar context = /** @type {ComponentContext} */ (component_context);\n\tvar effects = context.e;\n\n\tif (effects !== null) {\n\t\tcontext.e = null;\n\n\t\tfor (var fn of effects) {\n\t\t\tcreate_user_effect(fn);\n\t\t}\n\t}\n\n\tif (component !== undefined) {\n\t\tcontext.x = component;\n\t}\n\n\tcontext.i = true;\n\n\tcomponent_context = context.p;\n\n\tif (DEV) {\n\t\tdev_current_component_function = component_context?.function ?? null;\n\t}\n\n\treturn component ?? /** @type {T} */ ({});\n}\n\n/** @returns {boolean} */\nexport function is_runes() {\n\treturn !legacy_mode_flag || (component_context !== null && component_context.l === null);\n}\n\n/**\n * @param {string} name\n * @returns {Map<unknown, unknown>}\n */\nfunction get_or_init_context_map(name) {\n\tif (component_context === null) {\n\t\te.lifecycle_outside_component(name);\n\t}\n\n\treturn (component_context.c ??= new Map(get_parent_context(component_context) || undefined));\n}\n\n/**\n * @param {ComponentContext} component_context\n * @returns {Map<unknown, unknown> | null}\n */\nfunction get_parent_context(component_context) {\n\tlet parent = component_context.p;\n\twhile (parent !== null) {\n\t\tconst context_map = parent.c;\n\t\tif (context_map !== null) {\n\t\t\treturn context_map;\n\t\t}\n\t\tparent = parent.p;\n\t}\n\treturn null;\n}\n", "import { run_all } from '../../shared/utils.js';\nimport { is_flushing_sync } from '../reactivity/batch.js';\n\n/** @type {Array<() => void>} */\nlet micro_tasks = [];\n\nfunction run_micro_tasks() {\n\tvar tasks = micro_tasks;\n\tmicro_tasks = [];\n\trun_all(tasks);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_micro_task(fn) {\n\tif (micro_tasks.length === 0 && !is_flushing_sync) {\n\t\tvar tasks = micro_tasks;\n\t\tqueueMicrotask(() => {\n\t\t\t// If this is false, a flushSync happened in the meantime. Do _not_ run new scheduled microtasks in that case\n\t\t\t// as the ordering of microtasks would be broken at that point - consider this case:\n\t\t\t// - queue_micro_task schedules microtask A to flush task X\n\t\t\t// - synchronously after, flushSync runs, processing task X\n\t\t\t// - synchronously after, some other microtask B is scheduled, but not through queue_micro_task but for example a Promise.resolve() in user code\n\t\t\t// - synchronously after, queue_micro_task schedules microtask C to flush task Y\n\t\t\t// - one tick later, microtask A now resolves, flushing task Y before microtask B, which is incorrect\n\t\t\t// This if check prevents that race condition (that realistically will only happen in tests)\n\t\t\tif (tasks === micro_tasks) run_micro_tasks();\n\t\t});\n\t}\n\n\tmicro_tasks.push(fn);\n}\n\n/**\n * Synchronously run any queued tasks.\n */\nexport function flush_tasks() {\n\twhile (micro_tasks.length > 0) {\n\t\trun_micro_tasks();\n\t}\n}\n", "/** @import { Derived, Effect } from '#client' */\n/** @import { Boundary } from './dom/blocks/boundary.js' */\nimport { DEV } from 'esm-env';\nimport { FILENAME } from '../../constants.js';\nimport { is_firefox } from './dom/operations.js';\nimport { ERROR_VALUE, BOUNDARY_EFFECT, EFFECT_RAN } from './constants.js';\nimport { define_property, get_descriptor } from '../shared/utils.js';\nimport { active_effect, active_reaction } from './runtime.js';\n\nconst adjustments = new WeakMap();\n\n/**\n * @param {unknown} error\n */\nexport function handle_error(error) {\n\tvar effect = active_effect;\n\n\t// for unowned deriveds, don't throw until we read the value\n\tif (effect === null) {\n\t\t/** @type {Derived} */ (active_reaction).f |= ERROR_VALUE;\n\t\treturn error;\n\t}\n\n\tif (DEV && error instanceof Error && !adjustments.has(error)) {\n\t\tadjustments.set(error, get_adjustments(error, effect));\n\t}\n\n\tif ((effect.f & EFFECT_RAN) === 0) {\n\t\t// if the error occurred while creating this subtree, we let it\n\t\t// bubble up until it hits a boundary that can handle it\n\t\tif ((effect.f & BOUNDARY_EFFECT) === 0) {\n\t\t\tif (DEV && !effect.parent && error instanceof Error) {\n\t\t\t\tapply_adjustments(error);\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\n\t\t/** @type {Boundary} */ (effect.b).error(error);\n\t} else {\n\t\t// otherwise we bubble up the effect tree ourselves\n\t\tinvoke_error_boundary(error, effect);\n\t}\n}\n\n/**\n * @param {unknown} error\n * @param {Effect | null} effect\n */\nexport function invoke_error_boundary(error, effect) {\n\twhile (effect !== null) {\n\t\tif ((effect.f & BOUNDARY_EFFECT) !== 0) {\n\t\t\ttry {\n\t\t\t\t/** @type {Boundary} */ (effect.b).error(error);\n\t\t\t\treturn;\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\n\t\teffect = effect.parent;\n\t}\n\n\tif (DEV && error instanceof Error) {\n\t\tapply_adjustments(error);\n\t}\n\n\tthrow error;\n}\n\n/**\n * Add useful information to the error message/stack in development\n * @param {Error} error\n * @param {Effect} effect\n */\nfunction get_adjustments(error, effect) {\n\tconst message_descriptor = get_descriptor(error, 'message');\n\n\t// if the message was already changed and it's not configurable we can't change it\n\t// or it will throw a different error swallowing the original error\n\tif (message_descriptor && !message_descriptor.configurable) return;\n\n\tvar indent = is_firefox ? '  ' : '\\t';\n\tvar component_stack = `\\n${indent}in ${effect.fn?.name || '<unknown>'}`;\n\tvar context = effect.ctx;\n\n\twhile (context !== null) {\n\t\tcomponent_stack += `\\n${indent}in ${context.function?.[FILENAME].split('/').pop()}`;\n\t\tcontext = context.p;\n\t}\n\n\treturn {\n\t\tmessage: error.message + `\\n${component_stack}\\n`,\n\t\tstack: error.stack\n\t\t\t?.split('\\n')\n\t\t\t.filter((line) => !line.includes('svelte/src/internal'))\n\t\t\t.join('\\n')\n\t};\n}\n\n/**\n * @param {Error} error\n */\nfunction apply_adjustments(error) {\n\tconst adjusted = adjustments.get(error);\n\n\tif (adjusted) {\n\t\tdefine_property(error, 'message', {\n\t\t\tvalue: adjusted.message\n\t\t});\n\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: adjusted.stack\n\t\t});\n\t}\n}\n", "/** @import { Equals } from '#client' */\n\n/** @type {Equals} */\nexport function equals(value) {\n\treturn value === this.v;\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function safe_not_equal(a, b) {\n\treturn a != a\n\t\t? b == b\n\t\t: a !== b || (a !== null && typeof a === 'object') || typeof a === 'function';\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function not_equal(a, b) {\n\treturn a !== b;\n}\n\n/** @type {Equals} */\nexport function safe_equals(value) {\n\treturn !safe_not_equal(value, this.v);\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nvar bold = 'font-weight: bold';\nvar normal = 'font-weight: normal';\n\n/**\n * `<svelte:element this=\"%tag%\">` is a void element — it cannot have content\n * @param {string} tag\n */\nexport function dynamic_void_element_content(tag) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] dynamic_void_element_content\\n%c\\`<svelte:element this=\"${tag}\">\\` is a void element — it cannot have content\\nhttps://svelte.dev/e/dynamic_void_element_content`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/dynamic_void_element_content`);\n\t}\n}\n\n/**\n * The following properties cannot be cloned with `$state.snapshot` — the return value contains the originals:\n * \n * %properties%\n * @param {string | undefined | null} [properties]\n */\nexport function state_snapshot_uncloneable(properties) {\n\tif (DEV) {\n\t\tconsole.warn(\n\t\t\t`%c[svelte] state_snapshot_uncloneable\\n%c${properties\n\t\t\t\t? `The following properties cannot be cloned with \\`$state.snapshot\\` — the return value contains the originals:\n\n${properties}`\n\t\t\t\t: 'Value cannot be cloned with `$state.snapshot` — the original value was returned'}\\nhttps://svelte.dev/e/state_snapshot_uncloneable`,\n\t\t\tbold,\n\t\t\tnormal\n\t\t);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/state_snapshot_uncloneable`);\n\t}\n}", "/** @import { Snapshot } from './types' */\nimport { DEV } from 'esm-env';\nimport * as w from './warnings.js';\nimport { get_prototype_of, is_array, object_prototype } from './utils.js';\n\n/**\n * In dev, we keep track of which properties could not be cloned. In prod\n * we don't bother, but we keep a dummy array around so that the\n * signature stays the same\n * @type {string[]}\n */\nconst empty = [];\n\n/**\n * @template T\n * @param {T} value\n * @param {boolean} [skip_warning]\n * @param {boolean} [no_tojson]\n * @returns {Snapshot<T>}\n */\nexport function snapshot(value, skip_warning = false, no_tojson = false) {\n\tif (DEV && !skip_warning) {\n\t\t/** @type {string[]} */\n\t\tconst paths = [];\n\n\t\tconst copy = clone(value, new Map(), '', paths, null, no_tojson);\n\t\tif (paths.length === 1 && paths[0] === '') {\n\t\t\t// value could not be cloned\n\t\t\tw.state_snapshot_uncloneable();\n\t\t} else if (paths.length > 0) {\n\t\t\t// some properties could not be cloned\n\t\t\tconst slice = paths.length > 10 ? paths.slice(0, 7) : paths.slice(0, 10);\n\t\t\tconst excess = paths.length - slice.length;\n\n\t\t\tlet uncloned = slice.map((path) => `- <value>${path}`).join('\\n');\n\t\t\tif (excess > 0) uncloned += `\\n- ...and ${excess} more`;\n\n\t\t\tw.state_snapshot_uncloneable(uncloned);\n\t\t}\n\n\t\treturn copy;\n\t}\n\n\treturn clone(value, new Map(), '', empty, null, no_tojson);\n}\n\n/**\n * @template T\n * @param {T} value\n * @param {Map<T, Snapshot<T>>} cloned\n * @param {string} path\n * @param {string[]} paths\n * @param {null | T} [original] The original value, if `value` was produced from a `toJSON` call\n * @param {boolean} [no_tojson]\n * @returns {Snapshot<T>}\n */\nfunction clone(value, cloned, path, paths, original = null, no_tojson = false) {\n\tif (typeof value === 'object' && value !== null) {\n\t\tvar unwrapped = cloned.get(value);\n\t\tif (unwrapped !== undefined) return unwrapped;\n\n\t\tif (value instanceof Map) return /** @type {Snapshot<T>} */ (new Map(value));\n\t\tif (value instanceof Set) return /** @type {Snapshot<T>} */ (new Set(value));\n\n\t\tif (is_array(value)) {\n\t\t\tvar copy = /** @type {Snapshot<any>} */ (Array(value.length));\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < value.length; i += 1) {\n\t\t\t\tvar element = value[i];\n\t\t\t\tif (i in value) {\n\t\t\t\t\tcopy[i] = clone(element, cloned, DEV ? `${path}[${i}]` : path, paths, null, no_tojson);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (get_prototype_of(value) === object_prototype) {\n\t\t\t/** @type {Snapshot<any>} */\n\t\t\tcopy = {};\n\t\t\tcloned.set(value, copy);\n\n\t\t\tif (original !== null) {\n\t\t\t\tcloned.set(original, copy);\n\t\t\t}\n\n\t\t\tfor (var key in value) {\n\t\t\t\tcopy[key] = clone(\n\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\tvalue[key],\n\t\t\t\t\tcloned,\n\t\t\t\t\tDEV ? `${path}.${key}` : path,\n\t\t\t\t\tpaths,\n\t\t\t\t\tnull,\n\t\t\t\t\tno_tojson\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn copy;\n\t\t}\n\n\t\tif (value instanceof Date) {\n\t\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t\t}\n\n\t\tif (typeof (/** @type {T & { toJSON?: any } } */ (value).toJSON) === 'function' && !no_tojson) {\n\t\t\treturn clone(\n\t\t\t\t/** @type {T & { toJSON(): any } } */ (value).toJSON(),\n\t\t\t\tcloned,\n\t\t\t\tDEV ? `${path}.toJSON()` : path,\n\t\t\t\tpaths,\n\t\t\t\t// Associate the instance with the toJSON clone\n\t\t\t\tvalue\n\t\t\t);\n\t\t}\n\t}\n\n\tif (value instanceof EventTarget) {\n\t\t// can't be cloned\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n\n\ttry {\n\t\treturn /** @type {Snapshot<T>} */ (structuredClone(value));\n\t} catch (e) {\n\t\tif (DEV) {\n\t\t\tpaths.push(path);\n\t\t}\n\n\t\treturn /** @type {Snapshot<T>} */ (value);\n\t}\n}\n", "/** @import { Derived, Reaction, Value } from '#client' */\nimport { UNINITIALIZED } from '../../../constants.js';\nimport { snapshot } from '../../shared/clone.js';\nimport { define_property } from '../../shared/utils.js';\nimport { DERIVED, ASYNC, PROXY_PATH_SYMBOL, STATE_SYMBOL } from '#client/constants';\nimport { effect_tracking } from '../reactivity/effects.js';\nimport { active_reaction, untrack } from '../runtime.js';\n\n/**\n * @typedef {{\n *   traces: Error[];\n * }} TraceEntry\n */\n\n/** @type {{ reaction: Reaction | null, entries: Map<Value, TraceEntry> } | null} */\nexport let tracing_expressions = null;\n\n/**\n * @param {Value} signal\n * @param {TraceEntry} [entry]\n */\nfunction log_entry(signal, entry) {\n\tconst value = signal.v;\n\n\tif (value === UNINITIALIZED) {\n\t\treturn;\n\t}\n\n\tconst type = get_type(signal);\n\tconst current_reaction = /** @type {Reaction} */ (active_reaction);\n\tconst dirty = signal.wv > current_reaction.wv || current_reaction.wv === 0;\n\tconst style = dirty\n\t\t? 'color: CornflowerBlue; font-weight: bold'\n\t\t: 'color: grey; font-weight: normal';\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupCollapsed(\n\t\tsignal.label ? `%c${type}%c ${signal.label}` : `%c${type}%c`,\n\t\tstyle,\n\t\tdirty ? 'font-weight: normal' : style,\n\t\ttypeof value === 'object' && value !== null && STATE_SYMBOL in value\n\t\t\t? snapshot(value, true)\n\t\t\t: value\n\t);\n\n\tif (type === '$derived') {\n\t\tconst deps = new Set(/** @type {Derived} */ (signal).deps);\n\t\tfor (const dep of deps) {\n\t\t\tlog_entry(dep);\n\t\t}\n\t}\n\n\tif (signal.created) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.created);\n\t}\n\n\tif (dirty && signal.updated) {\n\t\tfor (const updated of signal.updated.values()) {\n\t\t\tif (updated.error) {\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.log(updated.error);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (entry) {\n\t\tfor (var trace of entry.traces) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(trace);\n\t\t}\n\t}\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupEnd();\n}\n\n/**\n * @param {Value} signal\n * @returns {'$state' | '$derived' | 'store'}\n */\nfunction get_type(signal) {\n\tif ((signal.f & (DERIVED | ASYNC)) !== 0) return '$derived';\n\treturn signal.label?.startsWith('$') ? 'store' : '$state';\n}\n\n/**\n * @template T\n * @param {() => string} label\n * @param {() => T} fn\n */\nexport function trace(label, fn) {\n\tvar previously_tracing_expressions = tracing_expressions;\n\n\ttry {\n\t\ttracing_expressions = { entries: new Map(), reaction: active_reaction };\n\n\t\tvar start = performance.now();\n\t\tvar value = fn();\n\t\tvar time = (performance.now() - start).toFixed(2);\n\n\t\tvar prefix = untrack(label);\n\n\t\tif (!effect_tracking()) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${prefix} %cran outside of an effect (${time}ms)`, 'color: grey');\n\t\t} else if (tracing_expressions.entries.size === 0) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${prefix} %cno reactive dependencies (${time}ms)`, 'color: grey');\n\t\t} else {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.group(`${prefix} %c(${time}ms)`, 'color: grey');\n\n\t\t\tvar entries = tracing_expressions.entries;\n\n\t\t\tuntrack(() => {\n\t\t\t\tfor (const [signal, traces] of entries) {\n\t\t\t\t\tlog_entry(signal, traces);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\ttracing_expressions = null;\n\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.groupEnd();\n\t\t}\n\n\t\treturn value;\n\t} finally {\n\t\ttracing_expressions = previously_tracing_expressions;\n\t}\n}\n\n/**\n * @param {string} label\n * @returns {Error & { stack: string } | null}\n */\nexport function get_stack(label) {\n\t// @ts-ignore stackTraceLimit doesn't exist everywhere\n\tconst limit = Error.stackTraceLimit;\n\n\t// @ts-ignore\n\tError.stackTraceLimit = Infinity;\n\tlet error = Error();\n\n\t// @ts-ignore\n\tError.stackTraceLimit = limit;\n\n\tconst stack = error.stack;\n\n\tif (!stack) return null;\n\n\tconst lines = stack.split('\\n');\n\tconst new_lines = ['\\n'];\n\n\tfor (let i = 0; i < lines.length; i++) {\n\t\tconst line = lines[i];\n\t\tconst posixified = line.replaceAll('\\\\', '/');\n\n\t\tif (line === 'Error') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (line.includes('validate_each_keys')) {\n\t\t\treturn null;\n\t\t}\n\n\t\tif (posixified.includes('svelte/src/internal') || posixified.includes('node_modules/.vite')) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tnew_lines.push(line);\n\t}\n\n\tif (new_lines.length === 1) {\n\t\treturn null;\n\t}\n\n\tdefine_property(error, 'stack', {\n\t\tvalue: new_lines.join('\\n')\n\t});\n\n\tdefine_property(error, 'name', {\n\t\tvalue: label\n\t});\n\n\treturn /** @type {Error & { stack: string }} */ (error);\n}\n\n/**\n * @param {Value} source\n * @param {string} label\n */\nexport function tag(source, label) {\n\tsource.label = label;\n\ttag_proxy(source.v, label);\n\n\treturn source;\n}\n\n/**\n * @param {unknown} value\n * @param {string} label\n */\nexport function tag_proxy(value, label) {\n\t// @ts-expect-error\n\tvalue?.[PROXY_PATH_SYMBOL]?.(label);\n\treturn value;\n}\n\n/**\n * @param {unknown} value\n */\nexport function label(value) {\n\tif (typeof value === 'symbol') return `Symbol(${value.description})`;\n\tif (typeof value === 'function') return '<function>';\n\tif (typeof value === 'object' && value) return '<object>';\n\treturn String(value);\n}\n", "import { get, tick, untrack } from '../internal/client/runtime.js';\nimport { effect_tracking, render_effect } from '../internal/client/reactivity/effects.js';\nimport { source, increment } from '../internal/client/reactivity/sources.js';\nimport { tag } from '../internal/client/dev/tracing.js';\nimport { DEV } from 'esm-env';\nimport { queue_micro_task } from '../internal/client/dom/task.js';\n\n/**\n * Returns a `subscribe` function that integrates external event-based systems with Svelte's reactivity.\n * It's particularly useful for integrating with web APIs like `MediaQuery`, `IntersectionObserver`, or `WebSocket`.\n *\n * If `subscribe` is called inside an effect (including indirectly, for example inside a getter),\n * the `start` callback will be called with an `update` function. Whenever `update` is called, the effect re-runs.\n *\n * If `start` returns a cleanup function, it will be called when the effect is destroyed.\n *\n * If `subscribe` is called in multiple effects, `start` will only be called once as long as the effects\n * are active, and the returned teardown function will only be called when all effects are destroyed.\n *\n * It's best understood with an example. Here's an implementation of [`MediaQuery`](https://svelte.dev/docs/svelte/svelte-reactivity#MediaQuery):\n *\n * ```js\n * import { createSubscriber } from 'svelte/reactivity';\n * import { on } from 'svelte/events';\n *\n * export class MediaQuery {\n * \t#query;\n * \t#subscribe;\n *\n * \tconstructor(query) {\n * \t\tthis.#query = window.matchMedia(`(${query})`);\n *\n * \t\tthis.#subscribe = createSubscriber((update) => {\n * \t\t\t// when the `change` event occurs, re-run any effects that read `this.current`\n * \t\t\tconst off = on(this.#query, 'change', update);\n *\n * \t\t\t// stop listening when all the effects are destroyed\n * \t\t\treturn () => off();\n * \t\t});\n * \t}\n *\n * \tget current() {\n * \t\t// This makes the getter reactive, if read in an effect\n * \t\tthis.#subscribe();\n *\n * \t\t// Return the current state of the query, whether or not we're in an effect\n * \t\treturn this.#query.matches;\n * \t}\n * }\n * ```\n * @param {(update: () => void) => (() => void) | void} start\n * @since 5.7.0\n */\nexport function createSubscriber(start) {\n\tlet subscribers = 0;\n\tlet version = source(0);\n\t/** @type {(() => void) | void} */\n\tlet stop;\n\n\tif (DEV) {\n\t\ttag(version, 'createSubscriber version');\n\t}\n\n\treturn () => {\n\t\tif (effect_tracking()) {\n\t\t\tget(version);\n\n\t\t\trender_effect(() => {\n\t\t\t\tif (subscribers === 0) {\n\t\t\t\t\tstop = untrack(() => start(() => increment(version)));\n\t\t\t\t}\n\n\t\t\t\tsubscribers += 1;\n\n\t\t\t\treturn () => {\n\t\t\t\t\tqueue_micro_task(() => {\n\t\t\t\t\t\t// Only count down after a microtask, else we would reach 0 before our own render effect reruns,\n\t\t\t\t\t\t// but reach 1 again when the tick callback of the prior teardown runs. That would mean we\n\t\t\t\t\t\t// re-subcribe unnecessarily and create a memory leak because the old subscription is never cleaned up.\n\t\t\t\t\t\tsubscribers -= 1;\n\n\t\t\t\t\t\tif (subscribers === 0) {\n\t\t\t\t\t\t\tstop?.();\n\t\t\t\t\t\t\tstop = undefined;\n\t\t\t\t\t\t\t// Increment the version to ensure any dependent deriveds are marked dirty when the subscription is picked up again later.\n\t\t\t\t\t\t\t// If we didn't do this then the comparison of write versions would determine that the derived has a later version than\n\t\t\t\t\t\t\t// the subscriber, and it would not be re-run.\n\t\t\t\t\t\t\tincrement(version);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t};\n\t\t\t});\n\t\t}\n\t};\n}\n", "/** @import { Effect, Source, TemplateNode, } from '#client' */\nimport {\n\tBOUNDARY_EFFECT,\n\tCOMMENT_NODE,\n\tEFFECT_PRESERVED,\n\tEFFECT_TRANSPARENT\n} from '#client/constants';\nimport { HYDRATION_START_ELSE } from '../../../../constants.js';\nimport { component_context, set_component_context } from '../../context.js';\nimport { handle_error, invoke_error_boundary } from '../../error-handling.js';\nimport {\n\tblock,\n\tbranch,\n\tdestroy_effect,\n\tmove_effect,\n\tpause_effect\n} from '../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tget,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tnext,\n\tskip_nodes,\n\tset_hydrate_node\n} from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport * as e from '../../errors.js';\nimport * as w from '../../warnings.js';\nimport { DEV } from 'esm-env';\nimport { Batch } from '../../reactivity/batch.js';\nimport { internal_set, source } from '../../reactivity/sources.js';\nimport { tag } from '../../dev/tracing.js';\nimport { createSubscriber } from '../../../../reactivity/create-subscriber.js';\nimport { create_text } from '../operations.js';\n\n/**\n * @typedef {{\n * \t onerror?: (error: unknown, reset: () => void) => void;\n *   failed?: (anchor: Node, error: () => unknown, reset: () => () => void) => void;\n *   pending?: (anchor: Node) => void;\n * }} BoundaryProps\n */\n\nvar flags = EFFECT_TRANSPARENT | EFFECT_PRESERVED | BOUNDARY_EFFECT;\n\n/**\n * @param {TemplateNode} node\n * @param {BoundaryProps} props\n * @param {((anchor: Node) => void)} children\n * @returns {void}\n */\nexport function boundary(node, props, children) {\n\tnew Boundary(node, props, children);\n}\n\nexport class Boundary {\n\t/** @type {Boundary | null} */\n\tparent;\n\n\t#pending = false;\n\n\t/** @type {TemplateNode} */\n\t#anchor;\n\n\t/** @type {TemplateNode | null} */\n\t#hydrate_open = hydrating ? hydrate_node : null;\n\n\t/** @type {BoundaryProps} */\n\t#props;\n\n\t/** @type {((anchor: Node) => void)} */\n\t#children;\n\n\t/** @type {Effect} */\n\t#effect;\n\n\t/** @type {Effect | null} */\n\t#main_effect = null;\n\n\t/** @type {Effect | null} */\n\t#pending_effect = null;\n\n\t/** @type {Effect | null} */\n\t#failed_effect = null;\n\n\t/** @type {DocumentFragment | null} */\n\t#offscreen_fragment = null;\n\n\t/** @type {TemplateNode | null} */\n\t#pending_anchor = null;\n\n\t#local_pending_count = 0;\n\t#pending_count = 0;\n\n\t#is_creating_fallback = false;\n\n\t/**\n\t * A source containing the number of pending async deriveds/expressions.\n\t * Only created if `$effect.pending()` is used inside the boundary,\n\t * otherwise updating the source results in needless `Batch.ensure()`\n\t * calls followed by no-op flushes\n\t * @type {Source<number> | null}\n\t */\n\t#effect_pending = null;\n\n\t#effect_pending_subscriber = createSubscriber(() => {\n\t\tthis.#effect_pending = source(this.#local_pending_count);\n\n\t\tif (DEV) {\n\t\t\ttag(this.#effect_pending, '$effect.pending()');\n\t\t}\n\n\t\treturn () => {\n\t\t\tthis.#effect_pending = null;\n\t\t};\n\t});\n\n\t/**\n\t * @param {TemplateNode} node\n\t * @param {BoundaryProps} props\n\t * @param {((anchor: Node) => void)} children\n\t */\n\tconstructor(node, props, children) {\n\t\tthis.#anchor = node;\n\t\tthis.#props = props;\n\t\tthis.#children = children;\n\n\t\tthis.parent = /** @type {Effect} */ (active_effect).b;\n\n\t\tthis.#pending = !!this.#props.pending;\n\n\t\tthis.#effect = block(() => {\n\t\t\t/** @type {Effect} */ (active_effect).b = this;\n\n\t\t\tif (hydrating) {\n\t\t\t\tconst comment = this.#hydrate_open;\n\t\t\t\thydrate_next();\n\n\t\t\t\tconst server_rendered_pending =\n\t\t\t\t\t/** @type {Comment} */ (comment).nodeType === COMMENT_NODE &&\n\t\t\t\t\t/** @type {Comment} */ (comment).data === HYDRATION_START_ELSE;\n\n\t\t\t\tif (server_rendered_pending) {\n\t\t\t\t\tthis.#hydrate_pending_content();\n\t\t\t\t} else {\n\t\t\t\t\tthis.#hydrate_resolved_content();\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tvar anchor = this.#get_anchor();\n\n\t\t\t\ttry {\n\t\t\t\t\tthis.#main_effect = branch(() => children(anchor));\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.error(error);\n\t\t\t\t}\n\n\t\t\t\tif (this.#pending_count > 0) {\n\t\t\t\t\tthis.#show_pending_snippet();\n\t\t\t\t} else {\n\t\t\t\t\tthis.#pending = false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn () => {\n\t\t\t\tthis.#pending_anchor?.remove();\n\t\t\t};\n\t\t}, flags);\n\n\t\tif (hydrating) {\n\t\t\tthis.#anchor = hydrate_node;\n\t\t}\n\t}\n\n\t#hydrate_resolved_content() {\n\t\ttry {\n\t\t\tthis.#main_effect = branch(() => this.#children(this.#anchor));\n\t\t} catch (error) {\n\t\t\tthis.error(error);\n\t\t}\n\n\t\t// Since server rendered resolved content, we never show pending state\n\t\t// Even if client-side async operations are still running, the content is already displayed\n\t\tthis.#pending = false;\n\t}\n\n\t#hydrate_pending_content() {\n\t\tconst pending = this.#props.pending;\n\t\tif (!pending) {\n\t\t\treturn;\n\t\t}\n\t\tthis.#pending_effect = branch(() => pending(this.#anchor));\n\n\t\tBatch.enqueue(() => {\n\t\t\tvar anchor = this.#get_anchor();\n\n\t\t\tthis.#main_effect = this.#run(() => {\n\t\t\t\tBatch.ensure();\n\t\t\t\treturn branch(() => this.#children(anchor));\n\t\t\t});\n\n\t\t\tif (this.#pending_count > 0) {\n\t\t\t\tthis.#show_pending_snippet();\n\t\t\t} else {\n\t\t\t\tpause_effect(/** @type {Effect} */ (this.#pending_effect), () => {\n\t\t\t\t\tthis.#pending_effect = null;\n\t\t\t\t});\n\n\t\t\t\tthis.#pending = false;\n\t\t\t}\n\t\t});\n\t}\n\n\t#get_anchor() {\n\t\tvar anchor = this.#anchor;\n\n\t\tif (this.#pending) {\n\t\t\tthis.#pending_anchor = create_text();\n\t\t\tthis.#anchor.before(this.#pending_anchor);\n\n\t\t\tanchor = this.#pending_anchor;\n\t\t}\n\n\t\treturn anchor;\n\t}\n\n\t/**\n\t * Returns `true` if the effect exists inside a boundary whose pending snippet is shown\n\t * @returns {boolean}\n\t */\n\tis_pending() {\n\t\treturn this.#pending || (!!this.parent && this.parent.is_pending());\n\t}\n\n\thas_pending_snippet() {\n\t\treturn !!this.#props.pending;\n\t}\n\n\t/**\n\t * @param {() => Effect | null} fn\n\t */\n\t#run(fn) {\n\t\tvar previous_effect = active_effect;\n\t\tvar previous_reaction = active_reaction;\n\t\tvar previous_ctx = component_context;\n\n\t\tset_active_effect(this.#effect);\n\t\tset_active_reaction(this.#effect);\n\t\tset_component_context(this.#effect.ctx);\n\n\t\ttry {\n\t\t\treturn fn();\n\t\t} catch (e) {\n\t\t\thandle_error(e);\n\t\t\treturn null;\n\t\t} finally {\n\t\t\tset_active_effect(previous_effect);\n\t\t\tset_active_reaction(previous_reaction);\n\t\t\tset_component_context(previous_ctx);\n\t\t}\n\t}\n\n\t#show_pending_snippet() {\n\t\tconst pending = /** @type {(anchor: Node) => void} */ (this.#props.pending);\n\n\t\tif (this.#main_effect !== null) {\n\t\t\tthis.#offscreen_fragment = document.createDocumentFragment();\n\t\t\tthis.#offscreen_fragment.append(/** @type {TemplateNode} */ (this.#pending_anchor));\n\t\t\tmove_effect(this.#main_effect, this.#offscreen_fragment);\n\t\t}\n\n\t\tif (this.#pending_effect === null) {\n\t\t\tthis.#pending_effect = branch(() => pending(this.#anchor));\n\t\t}\n\t}\n\n\t/**\n\t * Updates the pending count associated with the currently visible pending snippet,\n\t * if any, such that we can replace the snippet with content once work is done\n\t * @param {1 | -1} d\n\t */\n\t#update_pending_count(d) {\n\t\tif (!this.has_pending_snippet()) {\n\t\t\tif (this.parent) {\n\t\t\t\tthis.parent.#update_pending_count(d);\n\t\t\t}\n\n\t\t\t// if there's no parent, we're in a scope with no pending snippet\n\t\t\treturn;\n\t\t}\n\n\t\tthis.#pending_count += d;\n\n\t\tif (this.#pending_count === 0) {\n\t\t\tthis.#pending = false;\n\n\t\t\tif (this.#pending_effect) {\n\t\t\t\tpause_effect(this.#pending_effect, () => {\n\t\t\t\t\tthis.#pending_effect = null;\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (this.#offscreen_fragment) {\n\t\t\t\tthis.#anchor.before(this.#offscreen_fragment);\n\t\t\t\tthis.#offscreen_fragment = null;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Update the source that powers `$effect.pending()` inside this boundary,\n\t * and controls when the current `pending` snippet (if any) is removed.\n\t * Do not call from inside the class\n\t * @param {1 | -1} d\n\t */\n\tupdate_pending_count(d) {\n\t\tthis.#update_pending_count(d);\n\n\t\tthis.#local_pending_count += d;\n\n\t\tif (this.#effect_pending) {\n\t\t\tinternal_set(this.#effect_pending, this.#local_pending_count);\n\t\t}\n\t}\n\n\tget_effect_pending() {\n\t\tthis.#effect_pending_subscriber();\n\t\treturn get(/** @type {Source<number>} */ (this.#effect_pending));\n\t}\n\n\t/** @param {unknown} error */\n\terror(error) {\n\t\tvar onerror = this.#props.onerror;\n\t\tlet failed = this.#props.failed;\n\n\t\t// If we have nothing to capture the error, or if we hit an error while\n\t\t// rendering the fallback, re-throw for another boundary to handle\n\t\tif (this.#is_creating_fallback || (!onerror && !failed)) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (this.#main_effect) {\n\t\t\tdestroy_effect(this.#main_effect);\n\t\t\tthis.#main_effect = null;\n\t\t}\n\n\t\tif (this.#pending_effect) {\n\t\t\tdestroy_effect(this.#pending_effect);\n\t\t\tthis.#pending_effect = null;\n\t\t}\n\n\t\tif (this.#failed_effect) {\n\t\t\tdestroy_effect(this.#failed_effect);\n\t\t\tthis.#failed_effect = null;\n\t\t}\n\n\t\tif (hydrating) {\n\t\t\tset_hydrate_node(/** @type {TemplateNode} */ (this.#hydrate_open));\n\t\t\tnext();\n\t\t\tset_hydrate_node(skip_nodes());\n\t\t}\n\n\t\tvar did_reset = false;\n\t\tvar calling_on_error = false;\n\n\t\tconst reset = () => {\n\t\t\tif (did_reset) {\n\t\t\t\tw.svelte_boundary_reset_noop();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tdid_reset = true;\n\n\t\t\tif (calling_on_error) {\n\t\t\t\te.svelte_boundary_reset_onerror();\n\t\t\t}\n\n\t\t\t// If the failure happened while flushing effects, current_batch can be null\n\t\t\tBatch.ensure();\n\n\t\t\tthis.#local_pending_count = 0;\n\n\t\t\tif (this.#failed_effect !== null) {\n\t\t\t\tpause_effect(this.#failed_effect, () => {\n\t\t\t\t\tthis.#failed_effect = null;\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// we intentionally do not try to find the nearest pending boundary. If this boundary has one, we'll render it on reset\n\t\t\t// but it would be really weird to show the parent's boundary on a child reset.\n\t\t\tthis.#pending = this.has_pending_snippet();\n\n\t\t\tthis.#main_effect = this.#run(() => {\n\t\t\t\tthis.#is_creating_fallback = false;\n\t\t\t\treturn branch(() => this.#children(this.#anchor));\n\t\t\t});\n\n\t\t\tif (this.#pending_count > 0) {\n\t\t\t\tthis.#show_pending_snippet();\n\t\t\t} else {\n\t\t\t\tthis.#pending = false;\n\t\t\t}\n\t\t};\n\n\t\tvar previous_reaction = active_reaction;\n\n\t\ttry {\n\t\t\tset_active_reaction(null);\n\t\t\tcalling_on_error = true;\n\t\t\tonerror?.(error, reset);\n\t\t\tcalling_on_error = false;\n\t\t} catch (error) {\n\t\t\tinvoke_error_boundary(error, this.#effect && this.#effect.parent);\n\t\t} finally {\n\t\t\tset_active_reaction(previous_reaction);\n\t\t}\n\n\t\tif (failed) {\n\t\t\tqueue_micro_task(() => {\n\t\t\t\tthis.#failed_effect = this.#run(() => {\n\t\t\t\t\tBatch.ensure();\n\t\t\t\t\tthis.#is_creating_fallback = true;\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\treturn branch(() => {\n\t\t\t\t\t\t\tfailed(\n\t\t\t\t\t\t\t\tthis.#anchor,\n\t\t\t\t\t\t\t\t() => error,\n\t\t\t\t\t\t\t\t() => reset\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tinvoke_error_boundary(error, /** @type {Effect} */ (this.#effect.parent));\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tthis.#is_creating_fallback = false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t}\n}\n\nexport function get_boundary() {\n\treturn /** @type {Boundary} */ (/** @type {Effect} */ (active_effect).b);\n}\n\nexport function pending() {\n\tif (active_effect === null) {\n\t\te.effect_pending_outside_reaction();\n\t}\n\n\tvar boundary = active_effect.b;\n\n\tif (boundary === null) {\n\t\treturn 0; // TODO eventually we will need this to be global\n\t}\n\n\treturn boundary.get_effect_pending();\n}\n", "/** @import { EachItem, EachState, Effect, MaybeSource, Source, TemplateNode, TransitionManager, Value } from '#client' */\n/** @import { Batch } from '../../reactivity/batch.js'; */\nimport {\n\tEACH_INDEX_REACTIVE,\n\tEACH_IS_ANIMATED,\n\tEACH_IS_CONTROLLED,\n\tEACH_ITEM_IMMUTABLE,\n\tEACH_ITEM_REACTIVE,\n\tHYDRATION_END,\n\tHYDRATION_START_ELSE\n} from '../../../../constants.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tread_hydration_instruction,\n\tskip_nodes,\n\tset_hydrate_node,\n\tset_hydrating\n} from '../hydration.js';\nimport {\n\tclear_text_content,\n\tcreate_text,\n\tget_first_child,\n\tget_next_sibling,\n\tshould_defer_append\n} from '../operations.js';\nimport {\n\tblock,\n\tbranch,\n\tdestroy_effect,\n\trun_out_transitions,\n\tpause_children,\n\tpause_effect,\n\tresume_effect\n} from '../../reactivity/effects.js';\nimport { source, mutable_source, internal_set } from '../../reactivity/sources.js';\nimport { array_from, is_array } from '../../../shared/utils.js';\nimport { COMMENT_NODE, INERT } from '#client/constants';\nimport { queue_micro_task } from '../task.js';\nimport { get } from '../../runtime.js';\nimport { DEV } from 'esm-env';\nimport { derived_safe_equal } from '../../reactivity/deriveds.js';\nimport { current_batch } from '../../reactivity/batch.js';\n\n/**\n * The row of a keyed each block that is currently updating. We track this\n * so that `animate:` directives have something to attach themselves to\n * @type {EachItem | null}\n */\nexport let current_each_item = null;\n\n/** @param {EachItem | null} item */\nexport function set_current_each_item(item) {\n\tcurrent_each_item = item;\n}\n\n/**\n * @param {any} _\n * @param {number} i\n */\nexport function index(_, i) {\n\treturn i;\n}\n\n/**\n * Pause multiple effects simultaneously, and coordinate their\n * subsequent destruction. Used in each blocks\n * @param {EachState} state\n * @param {EachItem[]} to_destroy\n * @param {null | Node} controlled_anchor\n */\nfunction pause_effects(state, to_destroy, controlled_anchor) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\tvar length = to_destroy.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tpause_children(to_destroy[i].e, transitions, true);\n\t}\n\n\trun_out_transitions(transitions, () => {\n\t\t// If we're in a controlled each block (i.e. the block is the only child of an\n\t\t// element), and we are removing all items, _and_ there are no out transitions,\n\t\t// we can use the fast path — emptying the element and replacing the anchor\n\t\tvar fast_path = transitions.length === 0 && controlled_anchor !== null;\n\n\t\t// TODO only destroy effects if no pending batch needs them. otherwise,\n\t\t// just set `item.o` back to `false`\n\n\t\tif (fast_path) {\n\t\t\tvar anchor = /** @type {Element} */ (controlled_anchor);\n\t\t\tvar parent_node = /** @type {Element} */ (anchor.parentNode);\n\n\t\t\tclear_text_content(parent_node);\n\t\t\tparent_node.append(anchor);\n\n\t\t\tstate.items.clear();\n\t\t\tlink(state, to_destroy[0].prev, to_destroy[length - 1].next);\n\t\t}\n\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tvar item = to_destroy[i];\n\n\t\t\tif (!fast_path) {\n\t\t\t\tstate.items.delete(item.k);\n\t\t\t\tlink(state, item.prev, item.next);\n\t\t\t}\n\n\t\t\tdestroy_effect(item.e, !fast_path);\n\t\t}\n\n\t\tif (state.first === to_destroy[0]) {\n\t\t\tstate.first = to_destroy[0].prev;\n\t\t}\n\t});\n}\n\n/**\n * @template V\n * @param {Element | Comment} node The next sibling node, or the parent node if this is a 'controlled' block\n * @param {number} flags\n * @param {() => V[]} get_collection\n * @param {(value: V, index: number) => any} get_key\n * @param {(anchor: Node, item: MaybeSource<V>, index: MaybeSource<number>) => void} render_fn\n * @param {null | ((anchor: Node) => void)} fallback_fn\n * @returns {void}\n */\nexport function each(node, flags, get_collection, get_key, render_fn, fallback_fn = null) {\n\tvar anchor = node;\n\n\t/** @type {Map<any, EachItem>} */\n\tvar items = new Map();\n\n\t/** @type {EachItem | null} */\n\tvar first = null;\n\n\tvar is_controlled = (flags & EACH_IS_CONTROLLED) !== 0;\n\tvar is_reactive_value = (flags & EACH_ITEM_REACTIVE) !== 0;\n\tvar is_reactive_index = (flags & EACH_INDEX_REACTIVE) !== 0;\n\n\tif (is_controlled) {\n\t\tvar parent_node = /** @type {Element} */ (node);\n\n\t\tanchor = hydrating\n\t\t\t? set_hydrate_node(/** @type {Comment | Text} */ (get_first_child(parent_node)))\n\t\t\t: parent_node.appendChild(create_text());\n\t}\n\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\t/** @type {{ fragment: DocumentFragment | null, effect: Effect } | null} */\n\tvar fallback = null;\n\n\t// TODO: ideally we could use derived for runes mode but because of the ability\n\t// to use a store which can be mutated, we can't do that here as mutating a store\n\t// will still result in the collection array being the same from the store\n\tvar each_array = derived_safe_equal(() => {\n\t\tvar collection = get_collection();\n\n\t\treturn is_array(collection) ? collection : collection == null ? [] : array_from(collection);\n\t});\n\n\t/** @type {V[]} */\n\tvar array;\n\n\tvar first_run = true;\n\n\tfunction commit() {\n\t\treconcile(state, array, anchor, flags, get_key);\n\n\t\tif (fallback !== null) {\n\t\t\tif (array.length === 0) {\n\t\t\t\tif (fallback.fragment) {\n\t\t\t\t\tanchor.before(fallback.fragment);\n\t\t\t\t\tfallback.fragment = null;\n\t\t\t\t} else {\n\t\t\t\t\tresume_effect(fallback.effect);\n\t\t\t\t}\n\n\t\t\t\teffect.first = fallback.effect;\n\t\t\t} else {\n\t\t\t\tpause_effect(fallback.effect, () => {\n\t\t\t\t\t// TODO only null out if no pending batch needs it,\n\t\t\t\t\t// otherwise re-add `fallback.fragment` and move the\n\t\t\t\t\t// effect into it\n\t\t\t\t\tfallback = null;\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tvar effect = block(() => {\n\t\tarray = /** @type {V[]} */ (get(each_array));\n\t\tvar length = array.length;\n\n\t\t/** `true` if there was a hydration mismatch. Needs to be a `let` or else it isn't treeshaken out */\n\t\tlet mismatch = false;\n\n\t\tif (hydrating) {\n\t\t\tvar is_else = read_hydration_instruction(anchor) === HYDRATION_START_ELSE;\n\n\t\t\tif (is_else !== (length === 0)) {\n\t\t\t\t// hydration mismatch — remove the server-rendered DOM and start over\n\t\t\t\tanchor = skip_nodes();\n\n\t\t\t\tset_hydrate_node(anchor);\n\t\t\t\tset_hydrating(false);\n\t\t\t\tmismatch = true;\n\t\t\t}\n\t\t}\n\n\t\tvar keys = new Set();\n\t\tvar batch = /** @type {Batch} */ (current_batch);\n\t\tvar prev = null;\n\t\tvar defer = should_defer_append();\n\n\t\tfor (var i = 0; i < length; i += 1) {\n\t\t\tif (\n\t\t\t\thydrating &&\n\t\t\t\thydrate_node.nodeType === COMMENT_NODE &&\n\t\t\t\t/** @type {Comment} */ (hydrate_node).data === HYDRATION_END\n\t\t\t) {\n\t\t\t\t// The server rendered fewer items than expected,\n\t\t\t\t// so break out and continue appending non-hydrated items\n\t\t\t\tanchor = /** @type {Comment} */ (hydrate_node);\n\t\t\t\tmismatch = true;\n\t\t\t\tset_hydrating(false);\n\t\t\t}\n\n\t\t\tvar value = array[i];\n\t\t\tvar key = get_key(value, i);\n\n\t\t\tvar item = first_run ? null : items.get(key);\n\n\t\t\tif (item) {\n\t\t\t\t// update before reconciliation, to trigger any async updates\n\t\t\t\tif (is_reactive_value) {\n\t\t\t\t\tinternal_set(item.v, value);\n\t\t\t\t}\n\n\t\t\t\tif (is_reactive_index) {\n\t\t\t\t\tinternal_set(/** @type {Value<number>} */ (item.i), i);\n\t\t\t\t} else {\n\t\t\t\t\titem.i = i;\n\t\t\t\t}\n\n\t\t\t\tif (defer) {\n\t\t\t\t\tbatch.skipped_effects.delete(item.e);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\titem = create_item(\n\t\t\t\t\tfirst_run ? anchor : null,\n\t\t\t\t\tprev,\n\t\t\t\t\tvalue,\n\t\t\t\t\tkey,\n\t\t\t\t\ti,\n\t\t\t\t\trender_fn,\n\t\t\t\t\tflags,\n\t\t\t\t\tget_collection\n\t\t\t\t);\n\n\t\t\t\tif (first_run) {\n\t\t\t\t\titem.o = true;\n\n\t\t\t\t\tif (prev === null) {\n\t\t\t\t\t\tfirst = item;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tprev.next = item;\n\t\t\t\t\t}\n\n\t\t\t\t\tprev = item;\n\t\t\t\t}\n\n\t\t\t\titems.set(key, item);\n\t\t\t}\n\n\t\t\tkeys.add(key);\n\t\t}\n\n\t\tif (length === 0 && fallback_fn && !fallback) {\n\t\t\tif (first_run) {\n\t\t\t\tfallback = {\n\t\t\t\t\tfragment: null,\n\t\t\t\t\teffect: branch(() => fallback_fn(anchor))\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tvar fragment = document.createDocumentFragment();\n\t\t\t\tvar target = create_text();\n\t\t\t\tfragment.append(target);\n\n\t\t\t\tfallback = {\n\t\t\t\t\tfragment,\n\t\t\t\t\teffect: branch(() => fallback_fn(target))\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t// remove excess nodes\n\t\tif (hydrating && length > 0) {\n\t\t\tset_hydrate_node(skip_nodes());\n\t\t}\n\n\t\tif (!first_run) {\n\t\t\tif (defer) {\n\t\t\t\tfor (const [key, item] of items) {\n\t\t\t\t\tif (!keys.has(key)) {\n\t\t\t\t\t\tbatch.skipped_effects.add(item.e);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbatch.oncommit(commit);\n\t\t\t\tbatch.ondiscard(() => {\n\t\t\t\t\t// TODO presumably we need to do something here?\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tcommit();\n\t\t\t}\n\t\t}\n\n\t\tif (mismatch) {\n\t\t\t// continue in hydration mode\n\t\t\tset_hydrating(true);\n\t\t}\n\n\t\t// When we mount the each block for the first time, the collection won't be\n\t\t// connected to this effect as the effect hasn't finished running yet and its deps\n\t\t// won't be assigned. However, it's possible that when reconciling the each block\n\t\t// that a mutation occurred and it's made the collection MAYBE_DIRTY, so reading the\n\t\t// collection again can provide consistency to the reactive graph again as the deriveds\n\t\t// will now be `CLEAN`.\n\t\tget(each_array);\n\t});\n\n\t/** @type {EachState} */\n\tvar state = { effect, flags, items, first };\n\n\tfirst_run = false;\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n\n/**\n * Add, remove, or reorder items output by an each block as its input changes\n * @template V\n * @param {EachState} state\n * @param {Array<V>} array\n * @param {Element | Comment | Text} anchor\n * @param {number} flags\n * @param {(value: V, index: number) => any} get_key\n * @returns {void}\n */\nfunction reconcile(state, array, anchor, flags, get_key) {\n\tvar is_animated = (flags & EACH_IS_ANIMATED) !== 0;\n\n\tvar length = array.length;\n\tvar items = state.items;\n\tvar current = state.first;\n\n\t/** @type {undefined | Set<EachItem>} */\n\tvar seen;\n\n\t/** @type {EachItem | null} */\n\tvar prev = null;\n\n\t/** @type {undefined | Set<EachItem>} */\n\tvar to_animate;\n\n\t/** @type {EachItem[]} */\n\tvar matched = [];\n\n\t/** @type {EachItem[]} */\n\tvar stashed = [];\n\n\t/** @type {V} */\n\tvar value;\n\n\t/** @type {any} */\n\tvar key;\n\n\t/** @type {EachItem | undefined} */\n\tvar item;\n\n\t/** @type {number} */\n\tvar i;\n\n\tif (is_animated) {\n\t\tfor (i = 0; i < length; i += 1) {\n\t\t\tvalue = array[i];\n\t\t\tkey = get_key(value, i);\n\t\t\titem = /** @type {EachItem} */ (items.get(key));\n\n\t\t\titem.a?.measure();\n\t\t\t(to_animate ??= new Set()).add(item);\n\t\t}\n\t}\n\n\tfor (i = 0; i < length; i += 1) {\n\t\tvalue = array[i];\n\t\tkey = get_key(value, i);\n\n\t\titem = /** @type {EachItem} */ (items.get(key));\n\n\t\tstate.first ??= item;\n\n\t\tif (!item.o) {\n\t\t\titem.o = true;\n\n\t\t\tvar next = prev ? prev.next : current;\n\n\t\t\tlink(state, prev, item);\n\t\t\tlink(state, item, next);\n\n\t\t\tmove(item, next, anchor);\n\t\t\tprev = item;\n\n\t\t\tmatched = [];\n\t\t\tstashed = [];\n\n\t\t\tcurrent = prev.next;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif ((item.e.f & INERT) !== 0) {\n\t\t\tresume_effect(item.e);\n\t\t\tif (is_animated) {\n\t\t\t\titem.a?.unfix();\n\t\t\t\t(to_animate ??= new Set()).delete(item);\n\t\t\t}\n\t\t}\n\n\t\tif (item !== current) {\n\t\t\tif (seen !== undefined && seen.has(item)) {\n\t\t\t\tif (matched.length < stashed.length) {\n\t\t\t\t\t// more efficient to move later items to the front\n\t\t\t\t\tvar start = stashed[0];\n\t\t\t\t\tvar j;\n\n\t\t\t\t\tprev = start.prev;\n\n\t\t\t\t\tvar a = matched[0];\n\t\t\t\t\tvar b = matched[matched.length - 1];\n\n\t\t\t\t\tfor (j = 0; j < matched.length; j += 1) {\n\t\t\t\t\t\tmove(matched[j], start, anchor);\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (j = 0; j < stashed.length; j += 1) {\n\t\t\t\t\t\tseen.delete(stashed[j]);\n\t\t\t\t\t}\n\n\t\t\t\t\tlink(state, a.prev, b.next);\n\t\t\t\t\tlink(state, prev, a);\n\t\t\t\t\tlink(state, b, start);\n\n\t\t\t\t\tcurrent = start;\n\t\t\t\t\tprev = b;\n\t\t\t\t\ti -= 1;\n\n\t\t\t\t\tmatched = [];\n\t\t\t\t\tstashed = [];\n\t\t\t\t} else {\n\t\t\t\t\t// more efficient to move earlier items to the back\n\t\t\t\t\tseen.delete(item);\n\t\t\t\t\tmove(item, current, anchor);\n\n\t\t\t\t\tlink(state, item.prev, item.next);\n\t\t\t\t\tlink(state, item, prev === null ? state.first : prev.next);\n\t\t\t\t\tlink(state, prev, item);\n\n\t\t\t\t\tprev = item;\n\t\t\t\t}\n\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tmatched = [];\n\t\t\tstashed = [];\n\n\t\t\twhile (current !== null && current.k !== key) {\n\t\t\t\t// If the each block isn't inert and an item has an effect that is already inert,\n\t\t\t\t// skip over adding it to our seen Set as the item is already being handled\n\t\t\t\tif ((current.e.f & INERT) === 0) {\n\t\t\t\t\t(seen ??= new Set()).add(current);\n\t\t\t\t}\n\t\t\t\tstashed.push(current);\n\t\t\t\tcurrent = current.next;\n\t\t\t}\n\n\t\t\tif (current === null) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\titem = current;\n\t\t}\n\n\t\tmatched.push(item);\n\t\tprev = item;\n\t\tcurrent = item.next;\n\t}\n\n\tif (current !== null || seen !== undefined) {\n\t\tvar to_destroy = seen === undefined ? [] : array_from(seen);\n\n\t\twhile (current !== null) {\n\t\t\t// If the each block isn't inert, then inert effects are currently outroing and will be removed once the transition is finished\n\t\t\tif ((current.e.f & INERT) === 0) {\n\t\t\t\tto_destroy.push(current);\n\t\t\t}\n\t\t\tcurrent = current.next;\n\t\t}\n\n\t\tvar destroy_length = to_destroy.length;\n\n\t\tif (destroy_length > 0) {\n\t\t\tvar controlled_anchor = (flags & EACH_IS_CONTROLLED) !== 0 && length === 0 ? anchor : null;\n\n\t\t\tif (is_animated) {\n\t\t\t\tfor (i = 0; i < destroy_length; i += 1) {\n\t\t\t\t\tto_destroy[i].a?.measure();\n\t\t\t\t}\n\n\t\t\t\tfor (i = 0; i < destroy_length; i += 1) {\n\t\t\t\t\tto_destroy[i].a?.fix();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tpause_effects(state, to_destroy, controlled_anchor);\n\t\t}\n\t}\n\n\tif (is_animated) {\n\t\tqueue_micro_task(() => {\n\t\t\tif (to_animate === undefined) return;\n\t\t\tfor (item of to_animate) {\n\t\t\t\titem.a?.apply();\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * @template V\n * @param {Node | null} anchor\n * @param {EachItem | null} prev\n * @param {V} value\n * @param {unknown} key\n * @param {number} index\n * @param {(anchor: Node, item: V | Source<V>, index: number | Value<number>, collection: () => V[]) => void} render_fn\n * @param {number} flags\n * @param {() => V[]} get_collection\n * @returns {EachItem}\n */\nfunction create_item(anchor, prev, value, key, index, render_fn, flags, get_collection) {\n\tvar previous_each_item = current_each_item;\n\tvar reactive = (flags & EACH_ITEM_REACTIVE) !== 0;\n\tvar mutable = (flags & EACH_ITEM_IMMUTABLE) === 0;\n\n\tvar v = reactive ? (mutable ? mutable_source(value, false, false) : source(value)) : value;\n\tvar i = (flags & EACH_INDEX_REACTIVE) === 0 ? index : source(index);\n\n\tif (DEV && reactive) {\n\t\t// For tracing purposes, we need to link the source signal we create with the\n\t\t// collection + index so that tracing works as intended\n\t\t/** @type {Value} */ (v).trace = () => {\n\t\t\tvar collection_index = typeof i === 'number' ? index : i.v;\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unused-expressions\n\t\t\tget_collection()[collection_index];\n\t\t};\n\t}\n\n\t/** @type {EachItem} */\n\tvar item = {\n\t\ti,\n\t\tv,\n\t\tk: key,\n\t\ta: null,\n\t\t// @ts-expect-error\n\t\te: null,\n\t\to: false,\n\t\tprev,\n\t\tnext: null\n\t};\n\n\tcurrent_each_item = item;\n\n\ttry {\n\t\tif (anchor === null) {\n\t\t\tvar fragment = document.createDocumentFragment();\n\t\t\tfragment.append((anchor = create_text()));\n\t\t}\n\n\t\titem.e = branch(() => render_fn(/** @type {Node} */ (anchor), v, i, get_collection));\n\n\t\tif (prev !== null) {\n\t\t\t// we only need to set `prev.next = item`, because\n\t\t\t// `item.prev = prev` was set on initialization.\n\t\t\t// the effects themselves are already linked\n\t\t\tprev.next = item;\n\t\t}\n\n\t\treturn item;\n\t} finally {\n\t\tcurrent_each_item = previous_each_item;\n\t}\n}\n\n/**\n * @param {EachItem} item\n * @param {EachItem | null} next\n * @param {Text | Element | Comment} anchor\n */\nfunction move(item, next, anchor) {\n\tvar end = item.next ? /** @type {TemplateNode} */ (item.next.e.nodes_start) : anchor;\n\n\tvar dest = next ? /** @type {TemplateNode} */ (next.e.nodes_start) : anchor;\n\tvar node = /** @type {TemplateNode} */ (item.e.nodes_start);\n\n\twhile (node !== null && node !== end) {\n\t\tvar next_node = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tdest.before(node);\n\t\tnode = next_node;\n\t}\n}\n\n/**\n * @param {EachState} state\n * @param {EachItem | null} prev\n * @param {EachItem | null} next\n */\nfunction link(state, prev, next) {\n\tif (prev === null) {\n\t\tstate.first = next;\n\t\tstate.effect.first = next && next.e;\n\t} else {\n\t\tif (prev.e.next) {\n\t\t\tprev.e.next.prev = null;\n\t\t}\n\n\t\tprev.next = next;\n\t\tprev.e.next = next && next.e;\n\t}\n\n\tif (next === null) {\n\t\tstate.effect.last = prev && prev.e;\n\t} else {\n\t\tif (next.e.prev) {\n\t\t\tnext.e.prev.next = null;\n\t\t}\n\n\t\tnext.prev = prev;\n\t\tnext.e.prev = prev && prev.e;\n\t}\n}\n", "/** @import { Effect, TemplateNode, Value } from '#client' */\nimport { DESTROYED, STALE_REACTION } from '#client/constants';\nimport { DEV } from 'esm-env';\nimport {\n\tcomponent_context,\n\tdev_stack,\n\tis_runes,\n\tset_component_context,\n\tset_dev_stack\n} from '../context.js';\nimport { get_boundary } from '../dom/blocks/boundary.js';\nimport { invoke_error_boundary } from '../error-handling.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../runtime.js';\nimport { Batch, current_batch } from './batch.js';\nimport {\n\tasync_derived,\n\tcurrent_async_effect,\n\tderived,\n\tderived_safe_equal,\n\tset_from_async_derived\n} from './deriveds.js';\nimport { aborted } from './effects.js';\nimport { hydrate_next, hydrating, set_hydrate_node, skip_nodes } from '../dom/hydration.js';\nimport { current_each_item, set_current_each_item } from '../dom/blocks/each.js';\n\n/**\n * @param {Array<Promise<void>>} blockers\n * @param {Array<() => any>} sync\n * @param {Array<() => Promise<any>>} async\n * @param {(values: Value[]) => any} fn\n */\nexport function flatten(blockers, sync, async, fn) {\n\tconst d = is_runes() ? derived : derived_safe_equal;\n\n\tif (async.length === 0 && blockers.length === 0) {\n\t\tfn(sync.map(d));\n\t\treturn;\n\t}\n\n\tvar batch = current_batch;\n\tvar parent = /** @type {Effect} */ (active_effect);\n\n\tvar restore = capture();\n\n\tfunction run() {\n\t\tPromise.all(async.map((expression) => async_derived(expression)))\n\t\t\t.then((result) => {\n\t\t\t\trestore();\n\n\t\t\t\ttry {\n\t\t\t\t\tfn([...sync.map(d), ...result]);\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// ignore errors in blocks that have already been destroyed\n\t\t\t\t\tif ((parent.f & DESTROYED) === 0) {\n\t\t\t\t\t\tinvoke_error_boundary(error, parent);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbatch?.deactivate();\n\t\t\t\tunset_context();\n\t\t\t})\n\t\t\t.catch((error) => {\n\t\t\t\tinvoke_error_boundary(error, parent);\n\t\t\t});\n\t}\n\n\tif (blockers.length > 0) {\n\t\tPromise.all(blockers).then(() => {\n\t\t\trestore();\n\n\t\t\ttry {\n\t\t\t\treturn run();\n\t\t\t} finally {\n\t\t\t\tbatch?.deactivate();\n\t\t\t\tunset_context();\n\t\t\t}\n\t\t});\n\t} else {\n\t\trun();\n\t}\n}\n\n/**\n * @param {Array<Promise<void>>} blockers\n * @param {(values: Value[]) => any} fn\n */\nexport function run_after_blockers(blockers, fn) {\n\tvar each_item = current_each_item; // TODO should this be part of capture?\n\tflatten(blockers, [], [], (v) => {\n\t\tset_current_each_item(each_item);\n\t\tfn(v);\n\t});\n}\n\n/**\n * Captures the current effect context so that we can restore it after\n * some asynchronous work has happened (so that e.g. `await a + b`\n * causes `b` to be registered as a dependency).\n */\nexport function capture() {\n\tvar previous_effect = active_effect;\n\tvar previous_reaction = active_reaction;\n\tvar previous_component_context = component_context;\n\tvar previous_batch = current_batch;\n\n\tif (DEV) {\n\t\tvar previous_dev_stack = dev_stack;\n\t}\n\n\treturn function restore(activate_batch = true) {\n\t\tset_active_effect(previous_effect);\n\t\tset_active_reaction(previous_reaction);\n\t\tset_component_context(previous_component_context);\n\t\tif (activate_batch) previous_batch?.activate();\n\n\t\tif (DEV) {\n\t\t\tset_from_async_derived(null);\n\t\t\tset_dev_stack(previous_dev_stack);\n\t\t}\n\t};\n}\n\n/**\n * Wraps an `await` expression in such a way that the effect context that was\n * active before the expression evaluated can be reapplied afterwards —\n * `await a + b` becomes `(await $.save(a))() + b`\n * @template T\n * @param {Promise<T>} promise\n * @returns {Promise<() => T>}\n */\nexport async function save(promise) {\n\tvar restore = capture();\n\tvar value = await promise;\n\n\treturn () => {\n\t\trestore();\n\t\treturn value;\n\t};\n}\n\n/**\n * Reset `current_async_effect` after the `promise` resolves, so\n * that we can emit `await_reactivity_loss` warnings\n * @template T\n * @param {Promise<T>} promise\n * @returns {Promise<() => T>}\n */\nexport async function track_reactivity_loss(promise) {\n\tvar previous_async_effect = current_async_effect;\n\tvar value = await promise;\n\n\treturn () => {\n\t\tset_from_async_derived(previous_async_effect);\n\t\treturn value;\n\t};\n}\n\n/**\n * Used in `for await` loops in DEV, so\n * that we can emit `await_reactivity_loss` warnings\n * after each `async_iterator` result resolves and\n * after the `async_iterator` return resolves (if it runs)\n * @template T\n * @template TReturn\n * @param {Iterable<T> | AsyncIterable<T>} iterable\n * @returns {AsyncGenerator<T, TReturn | undefined>}\n */\nexport async function* for_await_track_reactivity_loss(iterable) {\n\t// This is based on the algorithms described in ECMA-262:\n\t// ForIn/OfBodyEvaluation\n\t// https://tc39.es/ecma262/multipage/ecmascript-language-statements-and-declarations.html#sec-runtime-semantics-forin-div-ofbodyevaluation-lhs-stmt-iterator-lhskind-labelset\n\t// AsyncIteratorClose\n\t// https://tc39.es/ecma262/multipage/abstract-operations.html#sec-asynciteratorclose\n\n\t/** @type {AsyncIterator<T, TReturn>} */\n\t// @ts-ignore\n\tconst iterator = iterable[Symbol.asyncIterator]?.() ?? iterable[Symbol.iterator]?.();\n\n\tif (iterator === undefined) {\n\t\tthrow new TypeError('value is not async iterable');\n\t}\n\n\t/** Whether the completion of the iterator was \"normal\", meaning it wasn't ended via `break` or a similar method */\n\tlet normal_completion = false;\n\ttry {\n\t\twhile (true) {\n\t\t\tconst { done, value } = (await track_reactivity_loss(iterator.next()))();\n\t\t\tif (done) {\n\t\t\t\tnormal_completion = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tyield value;\n\t\t}\n\t} finally {\n\t\t// If the iterator had a normal completion and `return` is defined on the iterator, call it and return the value\n\t\tif (normal_completion && iterator.return !== undefined) {\n\t\t\t// eslint-disable-next-line no-unsafe-finally\n\t\t\treturn /** @type {TReturn} */ ((await track_reactivity_loss(iterator.return()))().value);\n\t\t}\n\t}\n}\n\nexport function unset_context() {\n\tset_active_effect(null);\n\tset_active_reaction(null);\n\tset_component_context(null);\n\n\tif (DEV) {\n\t\tset_from_async_derived(null);\n\t\tset_dev_stack(null);\n\t}\n}\n\n/**\n * @param {TemplateNode} anchor\n * @param {(target: TemplateNode) => Promise<void>} fn\n */\nexport async function async_body(anchor, fn) {\n\tvar boundary = get_boundary();\n\tvar batch = /** @type {Batch} */ (current_batch);\n\tvar blocking = !boundary.is_pending();\n\n\tboundary.update_pending_count(1);\n\tbatch.increment(blocking);\n\n\tvar active = /** @type {Effect} */ (active_effect);\n\n\tvar was_hydrating = hydrating;\n\tvar next_hydrate_node = undefined;\n\n\tif (was_hydrating) {\n\t\thydrate_next();\n\t\tnext_hydrate_node = skip_nodes(false);\n\t}\n\n\ttry {\n\t\tvar promise = fn(anchor);\n\t} finally {\n\t\tif (next_hydrate_node) {\n\t\t\tset_hydrate_node(next_hydrate_node);\n\t\t\thydrate_next();\n\t\t}\n\t}\n\n\ttry {\n\t\tawait promise;\n\t} catch (error) {\n\t\tif (!aborted(active)) {\n\t\t\tinvoke_error_boundary(error, active);\n\t\t}\n\t} finally {\n\t\tboundary.update_pending_count(-1);\n\t\tbatch.decrement(blocking);\n\n\t\tunset_context();\n\t}\n}\n\n/**\n * @param {Array<() => void | Promise<void>>} thunks\n */\nexport function run(thunks) {\n\tconst restore = capture();\n\n\tvar boundary = get_boundary();\n\tvar batch = /** @type {Batch} */ (current_batch);\n\tvar blocking = !boundary.is_pending();\n\n\tboundary.update_pending_count(1);\n\tbatch.increment(blocking);\n\n\tvar active = /** @type {Effect} */ (active_effect);\n\n\t/** @type {null | { error: any }} */\n\tvar errored = null;\n\n\t/** @param {any} error */\n\tconst handle_error = (error) => {\n\t\terrored = { error }; // wrap in object in case a promise rejects with a falsy value\n\n\t\tif (!aborted(active)) {\n\t\t\tinvoke_error_boundary(error, active);\n\t\t}\n\t};\n\n\tvar promise = Promise.resolve(thunks[0]()).catch(handle_error);\n\n\tvar promises = [promise];\n\n\tfor (const fn of thunks.slice(1)) {\n\t\tpromise = promise\n\t\t\t.then(() => {\n\t\t\t\tif (errored) {\n\t\t\t\t\tthrow errored.error;\n\t\t\t\t}\n\n\t\t\t\tif (aborted(active)) {\n\t\t\t\t\tthrow STALE_REACTION;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\trestore();\n\t\t\t\t\treturn fn();\n\t\t\t\t} finally {\n\t\t\t\t\t// TODO do we need it here as well as below?\n\t\t\t\t\tunset_context();\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(handle_error)\n\t\t\t.finally(() => {\n\t\t\t\tunset_context();\n\t\t\t});\n\n\t\tpromises.push(promise);\n\t}\n\n\tpromise\n\t\t// wait one more tick, so that template effects are\n\t\t// guaranteed to run before `$effect(...)`\n\t\t.then(() => Promise.resolve())\n\t\t.finally(() => {\n\t\t\tboundary.update_pending_count(-1);\n\t\t\tbatch.decrement(blocking);\n\t\t});\n\n\treturn promises;\n}\n", "/** @import { Derived, Effect, Source } from '#client' */\n/** @import { Batch } from './batch.js'; */\nimport { DEV } from 'esm-env';\nimport {\n\tERROR_VALUE,\n\tCLEAN,\n\tDERIVED,\n\tDIRTY,\n\tEFFECT_PRESERVED,\n\tMAYBE_DIRTY,\n\tSTALE_REACTION,\n\tASYNC,\n\tWAS_MARKED,\n\tCONNECTED,\n\tDESTROYED\n} from '#client/constants';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tset_signal_status,\n\tupdate_reaction,\n\tincrement_write_version,\n\tset_active_effect,\n\tpush_reaction_value,\n\tis_destroying_effect\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport * as e from '../errors.js';\nimport * as w from '../warnings.js';\nimport { async_effect, destroy_effect, effect_tracking, teardown } from './effects.js';\nimport { eager_effects, internal_set, set_eager_effects, source } from './sources.js';\nimport { get_stack } from '../dev/tracing.js';\nimport { async_mode_flag, tracing_mode_flag } from '../../flags/index.js';\nimport { Boundary } from '../dom/blocks/boundary.js';\nimport { component_context } from '../context.js';\nimport { UNINITIALIZED } from '../../../constants.js';\nimport { batch_values, current_batch } from './batch.js';\nimport { unset_context } from './async.js';\nimport { deferred } from '../../shared/utils.js';\n\n/** @type {Effect | null} */\nexport let current_async_effect = null;\n\n/** @param {Effect | null} v */\nexport function set_from_async_derived(v) {\n\tcurrent_async_effect = v;\n}\n\nexport const recent_async_deriveds = new Set();\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived(fn) {\n\tvar flags = DERIVED | DIRTY;\n\tvar parent_derived =\n\t\tactive_reaction !== null && (active_reaction.f & DERIVED) !== 0\n\t\t\t? /** @type {Derived} */ (active_reaction)\n\t\t\t: null;\n\n\tif (active_effect !== null) {\n\t\t// Since deriveds are evaluated lazily, any effects created inside them are\n\t\t// created too late to ensure that the parent effect is added to the tree\n\t\tactive_effect.f |= EFFECT_PRESERVED;\n\t}\n\n\t/** @type {Derived<V>} */\n\tconst signal = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\teffects: null,\n\t\tequals,\n\t\tf: flags,\n\t\tfn,\n\t\treactions: null,\n\t\trv: 0,\n\t\tv: /** @type {V} */ (UNINITIALIZED),\n\t\twv: 0,\n\t\tparent: parent_derived ?? active_effect,\n\t\tac: null\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = get_stack('created at');\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {() => V | Promise<V>} fn\n * @param {string} [location] If provided, print a warning if the value is not read immediately after update\n * @returns {Promise<Source<V>>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function async_derived(fn, location) {\n\tlet parent = /** @type {Effect | null} */ (active_effect);\n\n\tif (parent === null) {\n\t\te.async_derived_orphan();\n\t}\n\n\tvar boundary = /** @type {Boundary} */ (parent.b);\n\n\tvar promise = /** @type {Promise<V>} */ (/** @type {unknown} */ (undefined));\n\tvar signal = source(/** @type {V} */ (UNINITIALIZED));\n\n\t// only suspend in async deriveds created on initialisation\n\tvar should_suspend = !active_reaction;\n\n\t/** @type {Map<Batch, ReturnType<typeof deferred<V>>>} */\n\tvar deferreds = new Map();\n\n\tasync_effect(() => {\n\t\tif (DEV) current_async_effect = active_effect;\n\n\t\t/** @type {ReturnType<typeof deferred<V>>} */\n\t\tvar d = deferred();\n\t\tpromise = d.promise;\n\n\t\ttry {\n\t\t\t// If this code is changed at some point, make sure to still access the then property\n\t\t\t// of fn() to read any signals it might access, so that we track them as dependencies.\n\t\t\t// We call `unset_context` to undo any `save` calls that happen inside `fn()`\n\t\t\tPromise.resolve(fn())\n\t\t\t\t.then(d.resolve, d.reject)\n\t\t\t\t.then(() => {\n\t\t\t\t\tif (batch === current_batch && batch.committed) {\n\t\t\t\t\t\t// if the batch was rejected as stale, we need to cleanup\n\t\t\t\t\t\t// after any `$.save(...)` calls inside `fn()`\n\t\t\t\t\t\tbatch.deactivate();\n\t\t\t\t\t}\n\n\t\t\t\t\tunset_context();\n\t\t\t\t});\n\t\t} catch (error) {\n\t\t\td.reject(error);\n\t\t\tunset_context();\n\t\t}\n\n\t\tif (DEV) current_async_effect = null;\n\n\t\tvar batch = /** @type {Batch} */ (current_batch);\n\n\t\tif (should_suspend) {\n\t\t\tvar blocking = !boundary.is_pending();\n\n\t\t\tboundary.update_pending_count(1);\n\t\t\tbatch.increment(blocking);\n\n\t\t\tdeferreds.get(batch)?.reject(STALE_REACTION);\n\t\t\tdeferreds.delete(batch); // delete to ensure correct order in Map iteration below\n\t\t\tdeferreds.set(batch, d);\n\t\t}\n\n\t\t/**\n\t\t * @param {any} value\n\t\t * @param {unknown} error\n\t\t */\n\t\tconst handler = (value, error = undefined) => {\n\t\t\tcurrent_async_effect = null;\n\n\t\t\tbatch.activate();\n\n\t\t\tif (error) {\n\t\t\t\tif (error !== STALE_REACTION) {\n\t\t\t\t\tsignal.f |= ERROR_VALUE;\n\n\t\t\t\t\t// @ts-expect-error the error is the wrong type, but we don't care\n\t\t\t\t\tinternal_set(signal, error);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif ((signal.f & ERROR_VALUE) !== 0) {\n\t\t\t\t\tsignal.f ^= ERROR_VALUE;\n\t\t\t\t}\n\n\t\t\t\tinternal_set(signal, value);\n\n\t\t\t\t// All prior async derived runs are now stale\n\t\t\t\tfor (const [b, d] of deferreds) {\n\t\t\t\t\tdeferreds.delete(b);\n\t\t\t\t\tif (b === batch) break;\n\t\t\t\t\td.reject(STALE_REACTION);\n\t\t\t\t}\n\n\t\t\t\tif (DEV && location !== undefined) {\n\t\t\t\t\trecent_async_deriveds.add(signal);\n\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tif (recent_async_deriveds.has(signal)) {\n\t\t\t\t\t\t\tw.await_waterfall(/** @type {string} */ (signal.label), location);\n\t\t\t\t\t\t\trecent_async_deriveds.delete(signal);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (should_suspend) {\n\t\t\t\tboundary.update_pending_count(-1);\n\t\t\t\tbatch.decrement(blocking);\n\t\t\t}\n\t\t};\n\n\t\td.promise.then(handler, (e) => handler(null, e || 'unknown'));\n\t});\n\n\tteardown(() => {\n\t\tfor (const d of deferreds.values()) {\n\t\t\td.reject(STALE_REACTION);\n\t\t}\n\t});\n\n\tif (DEV) {\n\t\t// add a flag that lets this be printed as a derived\n\t\t// when using `$inspect.trace()`\n\t\tsignal.f |= ASYNC;\n\t}\n\n\treturn new Promise((fulfil) => {\n\t\t/** @param {Promise<V>} p */\n\t\tfunction next(p) {\n\t\t\tfunction go() {\n\t\t\t\tif (p === promise) {\n\t\t\t\t\tfulfil(signal);\n\t\t\t\t} else {\n\t\t\t\t\t// if the effect re-runs before the initial promise\n\t\t\t\t\t// resolves, delay resolution until we have a value\n\t\t\t\t\tnext(promise);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tp.then(go, go);\n\t\t}\n\n\t\tnext(promise);\n\t});\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function user_derived(fn) {\n\tconst d = derived(fn);\n\n\tif (!async_mode_flag) push_reaction_value(d);\n\n\treturn d;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived_safe_equal(fn) {\n\tconst signal = derived(fn);\n\tsignal.equals = safe_equals;\n\treturn signal;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function destroy_derived_effects(derived) {\n\tvar effects = derived.effects;\n\n\tif (effects !== null) {\n\t\tderived.effects = null;\n\n\t\tfor (var i = 0; i < effects.length; i += 1) {\n\t\t\tdestroy_effect(/** @type {Effect} */ (effects[i]));\n\t\t}\n\t}\n}\n\n/**\n * The currently updating deriveds, used to detect infinite recursion\n * in dev mode and provide a nicer error than 'too much recursion'\n * @type {Derived[]}\n */\nlet stack = [];\n\n/**\n * @param {Derived} derived\n * @returns {Effect | null}\n */\nfunction get_derived_parent_effect(derived) {\n\tvar parent = derived.parent;\n\twhile (parent !== null) {\n\t\tif ((parent.f & DERIVED) === 0) {\n\t\t\t// The original parent effect might've been destroyed but the derived\n\t\t\t// is used elsewhere now - do not return the destroyed effect in that case\n\t\t\treturn (parent.f & DESTROYED) === 0 ? /** @type {Effect} */ (parent) : null;\n\t\t}\n\t\tparent = parent.parent;\n\t}\n\treturn null;\n}\n\n/**\n * @template T\n * @param {Derived} derived\n * @returns {T}\n */\nexport function execute_derived(derived) {\n\tvar value;\n\tvar prev_active_effect = active_effect;\n\n\tset_active_effect(get_derived_parent_effect(derived));\n\n\tif (DEV) {\n\t\tlet prev_eager_effects = eager_effects;\n\t\tset_eager_effects(new Set());\n\t\ttry {\n\t\t\tif (stack.includes(derived)) {\n\t\t\t\te.derived_references_self();\n\t\t\t}\n\n\t\t\tstack.push(derived);\n\n\t\t\tderived.f &= ~WAS_MARKED;\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t\tset_eager_effects(prev_eager_effects);\n\t\t\tstack.pop();\n\t\t}\n\t} else {\n\t\ttry {\n\t\t\tderived.f &= ~WAS_MARKED;\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function update_derived(derived) {\n\tvar value = execute_derived(derived);\n\n\tif (!derived.equals(value)) {\n\t\t// in a fork, we don't update the underlying value, just `batch_values`.\n\t\t// the underlying value will be updated when the fork is committed.\n\t\t// otherwise, the next time we get here after a 'real world' state\n\t\t// change, `derived.equals` may incorrectly return `true`\n\t\tif (!current_batch?.is_fork) {\n\t\t\tderived.v = value;\n\t\t}\n\n\t\tderived.wv = increment_write_version();\n\t}\n\n\t// don't mark derived clean if we're reading it inside a\n\t// cleanup function, or it will cache a stale value\n\tif (is_destroying_effect) {\n\t\treturn;\n\t}\n\n\t// During time traveling we don't want to reset the status so that\n\t// traversal of the graph in the other batches still happens\n\tif (batch_values !== null) {\n\t\t// only cache the value if we're in a tracking context, otherwise we won't\n\t\t// clear the cache in `mark_reactions` when dependencies are updated\n\t\tif (effect_tracking()) {\n\t\t\tbatch_values.set(derived, value);\n\t\t}\n\t} else {\n\t\tvar status = (derived.f & CONNECTED) === 0 ? MAYBE_DIRTY : CLEAN;\n\t\tset_signal_status(derived, status);\n\t}\n}\n", "/** @import { Derived, Effect, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tuntracked_writes,\n\tget,\n\tset_untracked_writes,\n\tset_signal_status,\n\tuntrack,\n\tincrement_write_version,\n\tupdate_effect,\n\tcurrent_sources,\n\tis_dirty,\n\tuntracking,\n\tis_destroying_effect,\n\tpush_reaction_value,\n\tset_is_updating_effect,\n\tis_updating_effect\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport {\n\tCLEAN,\n\tDERIVED,\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tEAGER_EFFECT,\n\tMAYBE_DIRTY,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tASYNC,\n\tWAS_MARKED,\n\tCONNECTED\n} from '#client/constants';\nimport * as e from '../errors.js';\nimport { legacy_mode_flag, tracing_mode_flag } from '../../flags/index.js';\nimport { get_stack, tag_proxy } from '../dev/tracing.js';\nimport { component_context, is_runes } from '../context.js';\nimport { Batch, batch_values, eager_block_effects, schedule_effect } from './batch.js';\nimport { proxy } from '../proxy.js';\nimport { execute_derived } from './deriveds.js';\n\n/** @type {Set<any>} */\nexport let eager_effects = new Set();\n\n/** @type {Map<Source, any>} */\nexport const old_values = new Map();\n\n/**\n * @param {Set<any>} v\n */\nexport function set_eager_effects(v) {\n\teager_effects = v;\n}\n\nlet eager_effects_deferred = false;\n\nexport function set_eager_effects_deferred() {\n\teager_effects_deferred = true;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n * @returns {Source<V>}\n */\n// TODO rename this to `state` throughout the codebase\nexport function source(v, stack) {\n\t/** @type {Value} */\n\tvar signal = {\n\t\tf: 0, // TODO ideally we could skip this altogether, but it causes type errors\n\t\tv,\n\t\treactions: null,\n\t\tequals,\n\t\trv: 0,\n\t\twv: 0\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = stack ?? get_stack('created at');\n\t\tsignal.updated = null;\n\t\tsignal.set_during_effect = false;\n\t\tsignal.trace = null;\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function state(v, stack) {\n\tconst s = source(v, stack);\n\n\tpush_reaction_value(s);\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {V} initial_value\n * @param {boolean} [immutable]\n * @returns {Source<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function mutable_source(initial_value, immutable = false, trackable = true) {\n\tconst s = source(initial_value);\n\tif (!immutable) {\n\t\ts.equals = safe_equals;\n\t}\n\n\t// bind the signal to the component context, in case we need to\n\t// track updates to trigger beforeUpdate/afterUpdate callbacks\n\tif (legacy_mode_flag && trackable && component_context !== null && component_context.l !== null) {\n\t\t(component_context.l.s ??= []).push(s);\n\t}\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {Value<V>} source\n * @param {V} value\n */\nexport function mutate(source, value) {\n\tset(\n\t\tsource,\n\t\tuntrack(() => get(source))\n\t);\n\treturn value;\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @param {boolean} [should_proxy]\n * @returns {V}\n */\nexport function set(source, value, should_proxy = false) {\n\tif (\n\t\tactive_reaction !== null &&\n\t\t// since we are untracking the function inside `$inspect.with` we need to add this check\n\t\t// to ensure we error if state is set inside an inspect effect\n\t\t(!untracking || (active_reaction.f & EAGER_EFFECT) !== 0) &&\n\t\tis_runes() &&\n\t\t(active_reaction.f & (DERIVED | BLOCK_EFFECT | ASYNC | EAGER_EFFECT)) !== 0 &&\n\t\t!current_sources?.includes(source)\n\t) {\n\t\te.state_unsafe_mutation();\n\t}\n\n\tlet new_value = should_proxy ? proxy(value) : value;\n\n\tif (DEV) {\n\t\ttag_proxy(new_value, /** @type {string} */ (source.label));\n\t}\n\n\treturn internal_set(source, new_value);\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @returns {V}\n */\nexport function internal_set(source, value) {\n\tif (!source.equals(value)) {\n\t\tvar old_value = source.v;\n\n\t\tif (is_destroying_effect) {\n\t\t\told_values.set(source, value);\n\t\t} else {\n\t\t\told_values.set(source, old_value);\n\t\t}\n\n\t\tsource.v = value;\n\n\t\tvar batch = Batch.ensure();\n\t\tbatch.capture(source, old_value);\n\n\t\tif (DEV) {\n\t\t\tif (tracing_mode_flag || active_effect !== null) {\n\t\t\t\tsource.updated ??= new Map();\n\n\t\t\t\t// For performance reasons, when not using $inspect.trace, we only start collecting stack traces\n\t\t\t\t// after the same source has been updated more than 5 times in the same flush cycle.\n\t\t\t\tconst count = (source.updated.get('')?.count ?? 0) + 1;\n\t\t\t\tsource.updated.set('', { error: /** @type {any} */ (null), count });\n\n\t\t\t\tif (tracing_mode_flag || count > 5) {\n\t\t\t\t\tconst error = get_stack('updated at');\n\n\t\t\t\t\tif (error !== null) {\n\t\t\t\t\t\tlet entry = source.updated.get(error.stack);\n\n\t\t\t\t\t\tif (!entry) {\n\t\t\t\t\t\t\tentry = { error, count: 0 };\n\t\t\t\t\t\t\tsource.updated.set(error.stack, entry);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tentry.count++;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (active_effect !== null) {\n\t\t\t\tsource.set_during_effect = true;\n\t\t\t}\n\t\t}\n\n\t\tif ((source.f & DERIVED) !== 0) {\n\t\t\t// if we are assigning to a dirty derived we set it to clean/maybe dirty but we also eagerly execute it to track the dependencies\n\t\t\tif ((source.f & DIRTY) !== 0) {\n\t\t\t\texecute_derived(/** @type {Derived} */ (source));\n\t\t\t}\n\n\t\t\tset_signal_status(source, (source.f & CONNECTED) !== 0 ? CLEAN : MAYBE_DIRTY);\n\t\t}\n\n\t\tsource.wv = increment_write_version();\n\n\t\tmark_reactions(source, DIRTY);\n\n\t\t// It's possible that the current reaction might not have up-to-date dependencies\n\t\t// whilst it's actively running. So in the case of ensuring it registers the reaction\n\t\t// properly for itself, we need to ensure the current effect actually gets\n\t\t// scheduled. i.e: `$effect(() => x++)`\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tactive_effect !== null &&\n\t\t\t(active_effect.f & CLEAN) !== 0 &&\n\t\t\t(active_effect.f & (BRANCH_EFFECT | ROOT_EFFECT)) === 0\n\t\t) {\n\t\t\tif (untracked_writes === null) {\n\t\t\t\tset_untracked_writes([source]);\n\t\t\t} else {\n\t\t\t\tuntracked_writes.push(source);\n\t\t\t}\n\t\t}\n\n\t\tif (!batch.is_fork && eager_effects.size > 0 && !eager_effects_deferred) {\n\t\t\tflush_eager_effects();\n\t\t}\n\t}\n\n\treturn value;\n}\n\nexport function flush_eager_effects() {\n\teager_effects_deferred = false;\n\tvar prev_is_updating_effect = is_updating_effect;\n\tset_is_updating_effect(true);\n\n\tconst inspects = Array.from(eager_effects);\n\n\ttry {\n\t\tfor (const effect of inspects) {\n\t\t\t// Mark clean inspect-effects as maybe dirty and then check their dirtiness\n\t\t\t// instead of just updating the effects - this way we avoid overfiring.\n\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t}\n\n\t\t\tif (is_dirty(effect)) {\n\t\t\t\tupdate_effect(effect);\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tset_is_updating_effect(prev_is_updating_effect);\n\t}\n\n\teager_effects.clear();\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update(source, d = 1) {\n\tvar value = get(source);\n\tvar result = d === 1 ? value++ : value--;\n\n\tset(source, value);\n\n\t// @ts-expect-error\n\treturn result;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update_pre(source, d = 1) {\n\tvar value = get(source);\n\n\t// @ts-expect-error\n\treturn set(source, d === 1 ? ++value : --value);\n}\n\n/**\n * Silently (without using `get`) increment a source\n * @param {Source<number>} source\n */\nexport function increment(source) {\n\tset(source, source.v + 1);\n}\n\n/**\n * @param {Value} signal\n * @param {number} status should be DIRTY or MAYBE_DIRTY\n * @returns {void}\n */\nfunction mark_reactions(signal, status) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tvar runes = is_runes();\n\tvar length = reactions.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar reaction = reactions[i];\n\t\tvar flags = reaction.f;\n\n\t\t// In legacy mode, skip the current effect to prevent infinite loops\n\t\tif (!runes && reaction === active_effect) continue;\n\n\t\t// Inspect effects need to run immediately, so that the stack trace makes sense\n\t\tif (DEV && (flags & EAGER_EFFECT) !== 0) {\n\t\t\teager_effects.add(reaction);\n\t\t\tcontinue;\n\t\t}\n\n\t\tvar not_dirty = (flags & DIRTY) === 0;\n\n\t\t// don't set a DIRTY reaction to MAYBE_DIRTY\n\t\tif (not_dirty) {\n\t\t\tset_signal_status(reaction, status);\n\t\t}\n\n\t\tif ((flags & DERIVED) !== 0) {\n\t\t\tvar derived = /** @type {Derived} */ (reaction);\n\n\t\t\tbatch_values?.delete(derived);\n\n\t\t\tif ((flags & WAS_MARKED) === 0) {\n\t\t\t\t// Only connected deriveds can be reliably unmarked right away\n\t\t\t\tif (flags & CONNECTED) {\n\t\t\t\t\treaction.f |= WAS_MARKED;\n\t\t\t\t}\n\n\t\t\t\tmark_reactions(derived, MAYBE_DIRTY);\n\t\t\t}\n\t\t} else if (not_dirty) {\n\t\t\tif ((flags & BLOCK_EFFECT) !== 0) {\n\t\t\t\tif (eager_block_effects !== null) {\n\t\t\t\t\teager_block_effects.add(/** @type {Effect} */ (reaction));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n", "/** @import { Fork } from 'svelte' */\n/** @import { Derived, Effect, Reaction, Source, Value } from '#client' */\nimport {\n\tBLOCK_EFFECT,\n\tBRANCH_EFFECT,\n\tCLEAN,\n\tDESTROYED,\n\tDIRTY,\n\tEFFECT,\n\tASYNC,\n\tINERT,\n\tRENDER_EFFECT,\n\tROOT_EFFECT,\n\tMAYBE_DIRTY,\n\tDERIVED,\n\tBOUNDARY_EFFECT,\n\tEAGER_EFFECT,\n\tHEAD_EFFECT,\n\tERROR_VALUE,\n\tWAS_MARKED\n} from '#client/constants';\nimport { async_mode_flag } from '../../flags/index.js';\nimport { deferred, define_property } from '../../shared/utils.js';\nimport {\n\tactive_effect,\n\tget,\n\tis_dirty,\n\tis_updating_effect,\n\tset_is_updating_effect,\n\tset_signal_status,\n\tupdate_effect\n} from '../runtime.js';\nimport * as e from '../errors.js';\nimport { flush_tasks, queue_micro_task } from '../dom/task.js';\nimport { DEV } from 'esm-env';\nimport { invoke_error_boundary } from '../error-handling.js';\nimport { flush_eager_effects, old_values, set_eager_effects, source, update } from './sources.js';\nimport { eager_effect, unlink_effect } from './effects.js';\n\n/**\n * @typedef {{\n *   parent: EffectTarget | null;\n *   effect: Effect | null;\n *   effects: Effect[];\n *   render_effects: Effect[];\n *   block_effects: Effect[];\n * }} EffectTarget\n */\n\n/** @type {Set<Batch>} */\nconst batches = new Set();\n\n/** @type {Batch | null} */\nexport let current_batch = null;\n\n/**\n * This is needed to avoid overwriting inputs in non-async mode\n * TODO 6.0 remove this, as non-async mode will go away\n * @type {Batch | null}\n */\nexport let previous_batch = null;\n\n/**\n * When time travelling (i.e. working in one batch, while other batches\n * still have ongoing work), we ignore the real values of affected\n * signals in favour of their values within the batch\n * @type {Map<Value, any> | null}\n */\nexport let batch_values = null;\n\n// TODO this should really be a property of `batch`\n/** @type {Effect[]} */\nlet queued_root_effects = [];\n\n/** @type {Effect | null} */\nlet last_scheduled_effect = null;\n\nlet is_flushing = false;\nexport let is_flushing_sync = false;\n\nexport class Batch {\n\tcommitted = false;\n\n\t/**\n\t * The current values of any sources that are updated in this batch\n\t * They keys of this map are identical to `this.#previous`\n\t * @type {Map<Source, any>}\n\t */\n\tcurrent = new Map();\n\n\t/**\n\t * The values of any sources that are updated in this batch _before_ those updates took place.\n\t * They keys of this map are identical to `this.#current`\n\t * @type {Map<Source, any>}\n\t */\n\tprevious = new Map();\n\n\t/**\n\t * When the batch is committed (and the DOM is updated), we need to remove old branches\n\t * and append new ones by calling the functions added inside (if/each/key/etc) blocks\n\t * @type {Set<() => void>}\n\t */\n\t#commit_callbacks = new Set();\n\n\t/**\n\t * If a fork is discarded, we need to destroy any effects that are no longer needed\n\t * @type {Set<(batch: Batch) => void>}\n\t */\n\t#discard_callbacks = new Set();\n\n\t/**\n\t * The number of async effects that are currently in flight\n\t */\n\t#pending = 0;\n\n\t/**\n\t * The number of async effects that are currently in flight, _not_ inside a pending boundary\n\t */\n\t#blocking_pending = 0;\n\n\t/**\n\t * A deferred that resolves when the batch is committed, used with `settled()`\n\t * TODO replace with Promise.withResolvers once supported widely enough\n\t * @type {{ promise: Promise<void>, resolve: (value?: any) => void, reject: (reason: unknown) => void } | null}\n\t */\n\t#deferred = null;\n\n\t/**\n\t * Deferred effects (which run after async work has completed) that are DIRTY\n\t * @type {Effect[]}\n\t */\n\t#dirty_effects = [];\n\n\t/**\n\t * Deferred effects that are MAYBE_DIRTY\n\t * @type {Effect[]}\n\t */\n\t#maybe_dirty_effects = [];\n\n\t/**\n\t * A set of branches that still exist, but will be destroyed when this batch\n\t * is committed — we skip over these during `process`\n\t * @type {Set<Effect>}\n\t */\n\tskipped_effects = new Set();\n\n\tis_fork = false;\n\n\tis_deferred() {\n\t\treturn this.is_fork || this.#blocking_pending > 0;\n\t}\n\n\t/**\n\t *\n\t * @param {Effect[]} root_effects\n\t */\n\tprocess(root_effects) {\n\t\tqueued_root_effects = [];\n\n\t\tprevious_batch = null;\n\n\t\tthis.apply();\n\n\t\t/** @type {EffectTarget} */\n\t\tvar target = {\n\t\t\tparent: null,\n\t\t\teffect: null,\n\t\t\teffects: [],\n\t\t\trender_effects: [],\n\t\t\tblock_effects: []\n\t\t};\n\n\t\tfor (const root of root_effects) {\n\t\t\tthis.#traverse_effect_tree(root, target);\n\t\t\t// Note: #traverse_effect_tree runs block effects eagerly, which can schedule effects,\n\t\t\t// which means queued_root_effects now may be filled again.\n\t\t}\n\n\t\tif (!this.is_fork) {\n\t\t\tthis.#resolve();\n\t\t}\n\n\t\tif (this.is_deferred()) {\n\t\t\tthis.#defer_effects(target.effects);\n\t\t\tthis.#defer_effects(target.render_effects);\n\t\t\tthis.#defer_effects(target.block_effects);\n\t\t} else {\n\t\t\t// If sources are written to, then work needs to happen in a separate batch, else prior sources would be mixed with\n\t\t\t// newly updated sources, which could lead to infinite loops when effects run over and over again.\n\t\t\tprevious_batch = this;\n\t\t\tcurrent_batch = null;\n\n\t\t\tflush_queued_effects(target.render_effects);\n\t\t\tflush_queued_effects(target.effects);\n\n\t\t\tprevious_batch = null;\n\n\t\t\tthis.#deferred?.resolve();\n\t\t}\n\n\t\tbatch_values = null;\n\t}\n\n\t/**\n\t * Traverse the effect tree, executing effects or stashing\n\t * them for later execution as appropriate\n\t * @param {Effect} root\n\t * @param {EffectTarget} target\n\t */\n\t#traverse_effect_tree(root, target) {\n\t\troot.f ^= CLEAN;\n\n\t\tvar effect = root.first;\n\n\t\twhile (effect !== null) {\n\t\t\tvar flags = effect.f;\n\t\t\tvar is_branch = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) !== 0;\n\t\t\tvar is_skippable_branch = is_branch && (flags & CLEAN) !== 0;\n\n\t\t\tvar skip = is_skippable_branch || (flags & INERT) !== 0 || this.skipped_effects.has(effect);\n\n\t\t\tif ((effect.f & BOUNDARY_EFFECT) !== 0 && effect.b?.is_pending()) {\n\t\t\t\ttarget = {\n\t\t\t\t\tparent: target,\n\t\t\t\t\teffect,\n\t\t\t\t\teffects: [],\n\t\t\t\t\trender_effects: [],\n\t\t\t\t\tblock_effects: []\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (!skip && effect.fn !== null) {\n\t\t\t\tif (is_branch) {\n\t\t\t\t\teffect.f ^= CLEAN;\n\t\t\t\t} else if ((flags & EFFECT) !== 0) {\n\t\t\t\t\ttarget.effects.push(effect);\n\t\t\t\t} else if (async_mode_flag && (flags & RENDER_EFFECT) !== 0) {\n\t\t\t\t\ttarget.render_effects.push(effect);\n\t\t\t\t} else if (is_dirty(effect)) {\n\t\t\t\t\tif ((effect.f & BLOCK_EFFECT) !== 0) target.block_effects.push(effect);\n\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t}\n\n\t\t\t\tvar child = effect.first;\n\n\t\t\t\tif (child !== null) {\n\t\t\t\t\teffect = child;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar parent = effect.parent;\n\t\t\teffect = effect.next;\n\n\t\t\twhile (effect === null && parent !== null) {\n\t\t\t\tif (parent === target.effect) {\n\t\t\t\t\t// TODO rather than traversing into pending boundaries and deferring the effects,\n\t\t\t\t\t// could we just attach the effects _to_ the pending boundary and schedule them\n\t\t\t\t\t// once the boundary is ready?\n\t\t\t\t\tthis.#defer_effects(target.effects);\n\t\t\t\t\tthis.#defer_effects(target.render_effects);\n\t\t\t\t\tthis.#defer_effects(target.block_effects);\n\n\t\t\t\t\ttarget = /** @type {EffectTarget} */ (target.parent);\n\t\t\t\t}\n\n\t\t\t\teffect = parent.next;\n\t\t\t\tparent = parent.parent;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {Effect[]} effects\n\t */\n\t#defer_effects(effects) {\n\t\tfor (const e of effects) {\n\t\t\tconst target = (e.f & DIRTY) !== 0 ? this.#dirty_effects : this.#maybe_dirty_effects;\n\t\t\ttarget.push(e);\n\n\t\t\t// Since we're not executing these effects now, we need to clear any WAS_MARKED flags\n\t\t\t// so that other batches can correctly reach these effects during their own traversal\n\t\t\tthis.#clear_marked(e.deps);\n\n\t\t\t// mark as clean so they get scheduled if they depend on pending async state\n\t\t\tset_signal_status(e, CLEAN);\n\t\t}\n\t}\n\n\t/**\n\t * @param {Value[] | null} deps\n\t */\n\t#clear_marked(deps) {\n\t\tif (deps === null) return;\n\n\t\tfor (const dep of deps) {\n\t\t\tif ((dep.f & DERIVED) === 0 || (dep.f & WAS_MARKED) === 0) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tdep.f ^= WAS_MARKED;\n\n\t\t\tthis.#clear_marked(/** @type {Derived} */ (dep).deps);\n\t\t}\n\t}\n\n\t/**\n\t * Associate a change to a given source with the current\n\t * batch, noting its previous and current values\n\t * @param {Source} source\n\t * @param {any} value\n\t */\n\tcapture(source, value) {\n\t\tif (!this.previous.has(source)) {\n\t\t\tthis.previous.set(source, value);\n\t\t}\n\n\t\t// Don't save errors in `batch_values`, or they won't be thrown in `runtime.js#get`\n\t\tif ((source.f & ERROR_VALUE) === 0) {\n\t\t\tthis.current.set(source, source.v);\n\t\t\tbatch_values?.set(source, source.v);\n\t\t}\n\t}\n\n\tactivate() {\n\t\tcurrent_batch = this;\n\t\tthis.apply();\n\t}\n\n\tdeactivate() {\n\t\t// If we're not the current batch, don't deactivate,\n\t\t// else we could create zombie batches that are never flushed\n\t\tif (current_batch !== this) return;\n\n\t\tcurrent_batch = null;\n\t\tbatch_values = null;\n\t}\n\n\tflush() {\n\t\tthis.activate();\n\n\t\tif (queued_root_effects.length > 0) {\n\t\t\tflush_effects();\n\n\t\t\tif (current_batch !== null && current_batch !== this) {\n\t\t\t\t// this can happen if a new batch was created during `flush_effects()`\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else if (this.#pending === 0) {\n\t\t\tthis.process([]); // TODO this feels awkward\n\t\t}\n\n\t\tthis.deactivate();\n\t}\n\n\tdiscard() {\n\t\tfor (const fn of this.#discard_callbacks) fn(this);\n\t\tthis.#discard_callbacks.clear();\n\t}\n\n\t#resolve() {\n\t\tif (this.#blocking_pending === 0) {\n\t\t\t// append/remove branches\n\t\t\tfor (const fn of this.#commit_callbacks) fn();\n\t\t\tthis.#commit_callbacks.clear();\n\t\t}\n\n\t\tif (this.#pending === 0) {\n\t\t\tthis.#commit();\n\t\t}\n\t}\n\n\t#commit() {\n\t\t// If there are other pending batches, they now need to be 'rebased' —\n\t\t// in other words, we re-run block/async effects with the newly\n\t\t// committed state, unless the batch in question has a more\n\t\t// recent value for a given source\n\t\tif (batches.size > 1) {\n\t\t\tthis.previous.clear();\n\n\t\t\tvar previous_batch_values = batch_values;\n\t\t\tvar is_earlier = true;\n\n\t\t\t/** @type {EffectTarget} */\n\t\t\tvar dummy_target = {\n\t\t\t\tparent: null,\n\t\t\t\teffect: null,\n\t\t\t\teffects: [],\n\t\t\t\trender_effects: [],\n\t\t\t\tblock_effects: []\n\t\t\t};\n\n\t\t\tfor (const batch of batches) {\n\t\t\t\tif (batch === this) {\n\t\t\t\t\tis_earlier = false;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t/** @type {Source[]} */\n\t\t\t\tconst sources = [];\n\n\t\t\t\tfor (const [source, value] of this.current) {\n\t\t\t\t\tif (batch.current.has(source)) {\n\t\t\t\t\t\tif (is_earlier && value !== batch.current.get(source)) {\n\t\t\t\t\t\t\t// bring the value up to date\n\t\t\t\t\t\t\tbatch.current.set(source, value);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// same value or later batch has more recent value,\n\t\t\t\t\t\t\t// no need to re-run these effects\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tsources.push(source);\n\t\t\t\t}\n\n\t\t\t\tif (sources.length === 0) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Re-run async/block effects that depend on distinct values changed in both batches\n\t\t\t\tconst others = [...batch.current.keys()].filter((s) => !this.current.has(s));\n\t\t\t\tif (others.length > 0) {\n\t\t\t\t\t// Avoid running queued root effects on the wrong branch\n\t\t\t\t\tvar prev_queued_root_effects = queued_root_effects;\n\t\t\t\t\tqueued_root_effects = [];\n\n\t\t\t\t\t/** @type {Set<Value>} */\n\t\t\t\t\tconst marked = new Set();\n\t\t\t\t\t/** @type {Map<Reaction, boolean>} */\n\t\t\t\t\tconst checked = new Map();\n\t\t\t\t\tfor (const source of sources) {\n\t\t\t\t\t\tmark_effects(source, others, marked, checked);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (queued_root_effects.length > 0) {\n\t\t\t\t\t\tcurrent_batch = batch;\n\t\t\t\t\t\tbatch.apply();\n\n\t\t\t\t\t\tfor (const root of queued_root_effects) {\n\t\t\t\t\t\t\tbatch.#traverse_effect_tree(root, dummy_target);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// TODO do we need to do anything with `target`? defer block effects?\n\n\t\t\t\t\t\tbatch.deactivate();\n\t\t\t\t\t}\n\n\t\t\t\t\tqueued_root_effects = prev_queued_root_effects;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcurrent_batch = null;\n\t\t\tbatch_values = previous_batch_values;\n\t\t}\n\n\t\tthis.committed = true;\n\t\tbatches.delete(this);\n\t}\n\n\t/**\n\t *\n\t * @param {boolean} blocking\n\t */\n\tincrement(blocking) {\n\t\tthis.#pending += 1;\n\t\tif (blocking) this.#blocking_pending += 1;\n\t}\n\n\t/**\n\t *\n\t * @param {boolean} blocking\n\t */\n\tdecrement(blocking) {\n\t\tthis.#pending -= 1;\n\t\tif (blocking) this.#blocking_pending -= 1;\n\n\t\tthis.revive();\n\t}\n\n\trevive() {\n\t\tfor (const e of this.#dirty_effects) {\n\t\t\tset_signal_status(e, DIRTY);\n\t\t\tschedule_effect(e);\n\t\t}\n\n\t\tfor (const e of this.#maybe_dirty_effects) {\n\t\t\tset_signal_status(e, MAYBE_DIRTY);\n\t\t\tschedule_effect(e);\n\t\t}\n\n\t\tthis.#dirty_effects = [];\n\t\tthis.#maybe_dirty_effects = [];\n\n\t\tthis.flush();\n\t}\n\n\t/** @param {() => void} fn */\n\toncommit(fn) {\n\t\tthis.#commit_callbacks.add(fn);\n\t}\n\n\t/** @param {(batch: Batch) => void} fn */\n\tondiscard(fn) {\n\t\tthis.#discard_callbacks.add(fn);\n\t}\n\n\tsettled() {\n\t\treturn (this.#deferred ??= deferred()).promise;\n\t}\n\n\tstatic ensure() {\n\t\tif (current_batch === null) {\n\t\t\tconst batch = (current_batch = new Batch());\n\t\t\tbatches.add(current_batch);\n\n\t\t\tif (!is_flushing_sync) {\n\t\t\t\tBatch.enqueue(() => {\n\t\t\t\t\tif (current_batch !== batch) {\n\t\t\t\t\t\t// a flushSync happened in the meantime\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tbatch.flush();\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\treturn current_batch;\n\t}\n\n\t/** @param {() => void} task */\n\tstatic enqueue(task) {\n\t\tqueue_micro_task(task);\n\t}\n\n\tapply() {\n\t\tif (!async_mode_flag || (!this.is_fork && batches.size === 1)) return;\n\n\t\t// if there are multiple batches, we are 'time travelling' —\n\t\t// we need to override values with the ones in this batch...\n\t\tbatch_values = new Map(this.current);\n\n\t\t// ...and undo changes belonging to other batches\n\t\tfor (const batch of batches) {\n\t\t\tif (batch === this) continue;\n\n\t\t\tfor (const [source, previous] of batch.previous) {\n\t\t\t\tif (!batch_values.has(source)) {\n\t\t\t\t\tbatch_values.set(source, previous);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Synchronously flush any pending updates.\n * Returns void if no callback is provided, otherwise returns the result of calling the callback.\n * @template [T=void]\n * @param {(() => T) | undefined} [fn]\n * @returns {T}\n */\nexport function flushSync(fn) {\n\tif (async_mode_flag && active_effect !== null) {\n\t\t// We disallow this because it creates super-hard to reason about stack trace and because it's generally a bad idea\n\t\te.flush_sync_in_effect();\n\t}\n\n\tvar was_flushing_sync = is_flushing_sync;\n\tis_flushing_sync = true;\n\n\ttry {\n\t\tvar result;\n\n\t\tif (fn) {\n\t\t\tif (current_batch !== null) {\n\t\t\t\tflush_effects();\n\t\t\t}\n\n\t\t\tresult = fn();\n\t\t}\n\n\t\twhile (true) {\n\t\t\tflush_tasks();\n\n\t\t\tif (queued_root_effects.length === 0) {\n\t\t\t\tcurrent_batch?.flush();\n\n\t\t\t\t// we need to check again, in case we just updated an `$effect.pending()`\n\t\t\t\tif (queued_root_effects.length === 0) {\n\t\t\t\t\t// this would be reset in `flush_effects()` but since we are early returning here,\n\t\t\t\t\t// we need to reset it here as well in case the first time there's 0 queued root effects\n\t\t\t\t\tlast_scheduled_effect = null;\n\n\t\t\t\t\treturn /** @type {T} */ (result);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tflush_effects();\n\t\t}\n\t} finally {\n\t\tis_flushing_sync = was_flushing_sync;\n\t}\n}\n\nfunction flush_effects() {\n\tvar was_updating_effect = is_updating_effect;\n\tis_flushing = true;\n\n\tvar source_stacks = DEV ? new Set() : null;\n\n\ttry {\n\t\tvar flush_count = 0;\n\t\tset_is_updating_effect(true);\n\n\t\twhile (queued_root_effects.length > 0) {\n\t\t\tvar batch = Batch.ensure();\n\n\t\t\tif (flush_count++ > 1000) {\n\t\t\t\tif (DEV) {\n\t\t\t\t\tvar updates = new Map();\n\n\t\t\t\t\tfor (const source of batch.current.keys()) {\n\t\t\t\t\t\tfor (const [stack, update] of source.updated ?? []) {\n\t\t\t\t\t\t\tvar entry = updates.get(stack);\n\n\t\t\t\t\t\t\tif (!entry) {\n\t\t\t\t\t\t\t\tentry = { error: update.error, count: 0 };\n\t\t\t\t\t\t\t\tupdates.set(stack, entry);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tentry.count += update.count;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (const update of updates.values()) {\n\t\t\t\t\t\tif (update.error) {\n\t\t\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\t\t\tconsole.error(update.error);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tinfinite_loop_guard();\n\t\t\t}\n\n\t\t\tbatch.process(queued_root_effects);\n\t\t\told_values.clear();\n\n\t\t\tif (DEV) {\n\t\t\t\tfor (const source of batch.current.keys()) {\n\t\t\t\t\t/** @type {Set<Source>} */ (source_stacks).add(source);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tis_flushing = false;\n\t\tset_is_updating_effect(was_updating_effect);\n\n\t\tlast_scheduled_effect = null;\n\n\t\tif (DEV) {\n\t\t\tfor (const source of /** @type {Set<Source>} */ (source_stacks)) {\n\t\t\t\tsource.updated = null;\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction infinite_loop_guard() {\n\ttry {\n\t\te.effect_update_depth_exceeded();\n\t} catch (error) {\n\t\tif (DEV) {\n\t\t\t// stack contains no useful information, replace it\n\t\t\tdefine_property(error, 'stack', { value: '' });\n\t\t}\n\n\t\t// Best effort: invoke the boundary nearest the most recent\n\t\t// effect and hope that it's relevant to the infinite loop\n\t\tinvoke_error_boundary(error, last_scheduled_effect);\n\t}\n}\n\n/** @type {Set<Effect> | null} */\nexport let eager_block_effects = null;\n\n/**\n * @param {Array<Effect>} effects\n * @returns {void}\n */\nfunction flush_queued_effects(effects) {\n\tvar length = effects.length;\n\tif (length === 0) return;\n\n\tvar i = 0;\n\n\twhile (i < length) {\n\t\tvar effect = effects[i++];\n\n\t\tif ((effect.f & (DESTROYED | INERT)) === 0 && is_dirty(effect)) {\n\t\t\teager_block_effects = new Set();\n\n\t\t\tupdate_effect(effect);\n\n\t\t\t// Effects with no dependencies or teardown do not get added to the effect tree.\n\t\t\t// Deferred effects (e.g. `$effect(...)`) _are_ added to the tree because we\n\t\t\t// don't know if we need to keep them until they are executed. Doing the check\n\t\t\t// here (rather than in `update_effect`) allows us to skip the work for\n\t\t\t// immediate effects.\n\t\t\tif (effect.deps === null && effect.first === null && effect.nodes_start === null) {\n\t\t\t\t// if there's no teardown or abort controller we completely unlink\n\t\t\t\t// the effect from the graph\n\t\t\t\tif (effect.teardown === null && effect.ac === null) {\n\t\t\t\t\t// remove this effect from the graph\n\t\t\t\t\tunlink_effect(effect);\n\t\t\t\t} else {\n\t\t\t\t\t// keep the effect in the graph, but free up some memory\n\t\t\t\t\teffect.fn = null;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If update_effect() has a flushSync() in it, we may have flushed another flush_queued_effects(),\n\t\t\t// which already handled this logic and did set eager_block_effects to null.\n\t\t\tif (eager_block_effects?.size > 0) {\n\t\t\t\told_values.clear();\n\n\t\t\t\tfor (const e of eager_block_effects) {\n\t\t\t\t\t// Skip eager effects that have already been unmounted\n\t\t\t\t\tif ((e.f & (DESTROYED | INERT)) !== 0) continue;\n\n\t\t\t\t\t// Run effects in order from ancestor to descendant, else we could run into nullpointers\n\t\t\t\t\t/** @type {Effect[]} */\n\t\t\t\t\tconst ordered_effects = [e];\n\t\t\t\t\tlet ancestor = e.parent;\n\t\t\t\t\twhile (ancestor !== null) {\n\t\t\t\t\t\tif (eager_block_effects.has(ancestor)) {\n\t\t\t\t\t\t\teager_block_effects.delete(ancestor);\n\t\t\t\t\t\t\tordered_effects.push(ancestor);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tancestor = ancestor.parent;\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (let j = ordered_effects.length - 1; j >= 0; j--) {\n\t\t\t\t\t\tconst e = ordered_effects[j];\n\t\t\t\t\t\t// Skip eager effects that have already been unmounted\n\t\t\t\t\t\tif ((e.f & (DESTROYED | INERT)) !== 0) continue;\n\t\t\t\t\t\tupdate_effect(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\teager_block_effects.clear();\n\t\t\t}\n\t\t}\n\t}\n\n\teager_block_effects = null;\n}\n\n/**\n * This is similar to `mark_reactions`, but it only marks async/block effects\n * depending on `value` and at least one of the other `sources`, so that\n * these effects can re-run after another batch has been committed\n * @param {Value} value\n * @param {Source[]} sources\n * @param {Set<Value>} marked\n * @param {Map<Reaction, boolean>} checked\n */\nfunction mark_effects(value, sources, marked, checked) {\n\tif (marked.has(value)) return;\n\tmarked.add(value);\n\n\tif (value.reactions !== null) {\n\t\tfor (const reaction of value.reactions) {\n\t\t\tconst flags = reaction.f;\n\n\t\t\tif ((flags & DERIVED) !== 0) {\n\t\t\t\tmark_effects(/** @type {Derived} */ (reaction), sources, marked, checked);\n\t\t\t} else if (\n\t\t\t\t(flags & (ASYNC | BLOCK_EFFECT)) !== 0 &&\n\t\t\t\t(flags & DIRTY) === 0 && // we may have scheduled this one already\n\t\t\t\tdepends_on(reaction, sources, checked)\n\t\t\t) {\n\t\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * When committing a fork, we need to trigger eager effects so that\n * any `$state.eager(...)` expressions update immediately. This\n * function allows us to discover them\n * @param {Value} value\n * @param {Set<Effect>} effects\n */\nfunction mark_eager_effects(value, effects) {\n\tif (value.reactions === null) return;\n\n\tfor (const reaction of value.reactions) {\n\t\tconst flags = reaction.f;\n\n\t\tif ((flags & DERIVED) !== 0) {\n\t\t\tmark_eager_effects(/** @type {Derived} */ (reaction), effects);\n\t\t} else if ((flags & EAGER_EFFECT) !== 0) {\n\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\teffects.add(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n\n/**\n * @param {Reaction} reaction\n * @param {Source[]} sources\n * @param {Map<Reaction, boolean>} checked\n */\nfunction depends_on(reaction, sources, checked) {\n\tconst depends = checked.get(reaction);\n\tif (depends !== undefined) return depends;\n\n\tif (reaction.deps !== null) {\n\t\tfor (const dep of reaction.deps) {\n\t\t\tif (sources.includes(dep)) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif ((dep.f & DERIVED) !== 0 && depends_on(/** @type {Derived} */ (dep), sources, checked)) {\n\t\t\t\tchecked.set(/** @type {Derived} */ (dep), true);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\n\tchecked.set(reaction, false);\n\n\treturn false;\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function schedule_effect(signal) {\n\tvar effect = (last_scheduled_effect = signal);\n\n\twhile (effect.parent !== null) {\n\t\teffect = effect.parent;\n\t\tvar flags = effect.f;\n\n\t\t// if the effect is being scheduled because a parent (each/await/etc) block\n\t\t// updated an internal source, bail out or we'll cause a second flush\n\t\tif (\n\t\t\tis_flushing &&\n\t\t\teffect === active_effect &&\n\t\t\t(flags & BLOCK_EFFECT) !== 0 &&\n\t\t\t(flags & HEAD_EFFECT) === 0\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ((flags & (ROOT_EFFECT | BRANCH_EFFECT)) !== 0) {\n\t\t\tif ((flags & CLEAN) === 0) return;\n\t\t\teffect.f ^= CLEAN;\n\t\t}\n\t}\n\n\tqueued_root_effects.push(effect);\n}\n\n/** @type {Source<number>[]} */\nlet eager_versions = [];\n\nfunction eager_flush() {\n\ttry {\n\t\tflushSync(() => {\n\t\t\tfor (const version of eager_versions) {\n\t\t\t\tupdate(version);\n\t\t\t}\n\t\t});\n\t} finally {\n\t\teager_versions = [];\n\t}\n}\n\n/**\n * Implementation of `$state.eager(fn())`\n * @template T\n * @param {() => T} fn\n * @returns {T}\n */\nexport function eager(fn) {\n\tvar version = source(0);\n\tvar initial = true;\n\tvar value = /** @type {T} */ (undefined);\n\n\tget(version);\n\n\teager_effect(() => {\n\t\tif (initial) {\n\t\t\t// the first time this runs, we create an eager effect\n\t\t\t// that will run eagerly whenever the expression changes\n\t\t\tvar previous_batch_values = batch_values;\n\n\t\t\ttry {\n\t\t\t\tbatch_values = null;\n\t\t\t\tvalue = fn();\n\t\t\t} finally {\n\t\t\t\tbatch_values = previous_batch_values;\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t// the second time this effect runs, it's to schedule a\n\t\t// `version` update. since this will recreate the effect,\n\t\t// we don't need to evaluate the expression here\n\t\tif (eager_versions.length === 0) {\n\t\t\tqueue_micro_task(eager_flush);\n\t\t}\n\n\t\teager_versions.push(version);\n\t});\n\n\tinitial = false;\n\n\treturn value;\n}\n\n/**\n * Creates a 'fork', in which state changes are evaluated but not applied to the DOM.\n * This is useful for speculatively loading data (for example) when you suspect that\n * the user is about to take some action.\n *\n * Frameworks like SvelteKit can use this to preload data when the user touches or\n * hovers over a link, making any subsequent navigation feel instantaneous.\n *\n * The `fn` parameter is a synchronous function that modifies some state. The\n * state changes will be reverted after the fork is initialised, then reapplied\n * if and when the fork is eventually committed.\n *\n * When it becomes clear that a fork will _not_ be committed (e.g. because the\n * user navigated elsewhere), it must be discarded to avoid leaking memory.\n *\n * @param {() => void} fn\n * @returns {Fork}\n * @since 5.42\n */\nexport function fork(fn) {\n\tif (!async_mode_flag) {\n\t\te.experimental_async_fork();\n\t}\n\n\tif (current_batch !== null) {\n\t\te.fork_timing();\n\t}\n\n\tvar batch = Batch.ensure();\n\tbatch.is_fork = true;\n\n\tvar committed = false;\n\tvar settled = batch.settled();\n\n\tflushSync(fn);\n\n\t// revert state changes\n\tfor (var [source, value] of batch.previous) {\n\t\tsource.v = value;\n\t}\n\n\treturn {\n\t\tcommit: async () => {\n\t\t\tif (committed) {\n\t\t\t\tawait settled;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!batches.has(batch)) {\n\t\t\t\te.fork_discarded();\n\t\t\t}\n\n\t\t\tcommitted = true;\n\n\t\t\tbatch.is_fork = false;\n\n\t\t\t// apply changes\n\t\t\tfor (var [source, value] of batch.current) {\n\t\t\t\tsource.v = value;\n\t\t\t}\n\n\t\t\t// trigger any `$state.eager(...)` expressions with the new state.\n\t\t\t// eager effects don't get scheduled like other effects, so we\n\t\t\t// can't just encounter them during traversal, we need to\n\t\t\t// proactively flush them\n\t\t\t// TODO maybe there's a better implementation?\n\t\t\tflushSync(() => {\n\t\t\t\t/** @type {Set<Effect>} */\n\t\t\t\tvar eager_effects = new Set();\n\n\t\t\t\tfor (var source of batch.current.keys()) {\n\t\t\t\t\tmark_eager_effects(source, eager_effects);\n\t\t\t\t}\n\n\t\t\t\tset_eager_effects(eager_effects);\n\t\t\t\tflush_eager_effects();\n\t\t\t});\n\n\t\t\tbatch.revive();\n\t\t\tawait settled;\n\t\t},\n\t\tdiscard: () => {\n\t\t\tif (!committed && batches.has(batch)) {\n\t\t\t\tbatches.delete(batch);\n\t\t\t\tbatch.discard();\n\t\t\t}\n\t\t}\n\t};\n}\n\n/**\n * Forcibly remove all current batches, to prevent cross-talk between tests\n */\nexport function clear() {\n\tbatches.clear();\n}\n", "import { hydrating } from '../hydration.js';\nimport { clear_text_content, get_first_child } from '../operations.js';\nimport { queue_micro_task } from '../task.js';\n\n/**\n * @param {HTMLElement} dom\n * @param {boolean} value\n * @returns {void}\n */\nexport function autofocus(dom, value) {\n\tif (value) {\n\t\tconst body = document.body;\n\t\tdom.autofocus = true;\n\n\t\tqueue_micro_task(() => {\n\t\t\tif (document.activeElement === body) {\n\t\t\t\tdom.focus();\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * The child of a textarea actually corresponds to the defaultValue property, so we need\n * to remove it upon hydration to avoid a bug when someone resets the form value.\n * @param {HTMLTextAreaElement} dom\n * @returns {void}\n */\nexport function remove_textarea_child(dom) {\n\tif (hydrating && get_first_child(dom) !== null) {\n\t\tclear_text_content(dom);\n\t}\n}\n\nlet listening_to_form_reset = false;\n\nexport function add_form_reset_listener() {\n\tif (!listening_to_form_reset) {\n\t\tlistening_to_form_reset = true;\n\t\tdocument.addEventListener(\n\t\t\t'reset',\n\t\t\t(evt) => {\n\t\t\t\t// Needs to happen one tick later or else the dom properties of the form\n\t\t\t\t// elements have not updated to their reset values yet\n\t\t\t\tPromise.resolve().then(() => {\n\t\t\t\t\tif (!evt.defaultPrevented) {\n\t\t\t\t\t\tfor (const e of /**@type {HTMLFormElement} */ (evt.target).elements) {\n\t\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\t\te.__on_r?.();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// In the capture phase to guarantee we get noticed of it (no possiblity of stopPropagation)\n\t\t\t{ capture: true }\n\t\t);\n\t}\n}\n", "import { teardown } from '../../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../../runtime.js';\nimport { add_form_reset_listener } from '../misc.js';\n\n/**\n * Fires the handler once immediately (unless corresponding arg is set to `false`),\n * then listens to the given events until the render effect context is destroyed\n * @param {EventTarget} target\n * @param {Array<string>} events\n * @param {(event?: Event) => void} handler\n * @param {any} call_handler_immediately\n */\nexport function listen(target, events, handler, call_handler_immediately = true) {\n\tif (call_handler_immediately) {\n\t\thandler();\n\t}\n\n\tfor (var name of events) {\n\t\ttarget.addEventListener(name, handler);\n\t}\n\n\tteardown(() => {\n\t\tfor (var name of events) {\n\t\t\ttarget.removeEventListener(name, handler);\n\t\t}\n\t});\n}\n\n/**\n * @template T\n * @param {() => T} fn\n */\nexport function without_reactive_context(fn) {\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\ttry {\n\t\treturn fn();\n\t} finally {\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * Listen to the given event, and then instantiate a global form reset listener if not already done,\n * to notify all bindings when the form is reset\n * @param {HTMLElement} element\n * @param {string} event\n * @param {(is_reset?: true) => void} handler\n * @param {(is_reset?: true) => void} [on_reset]\n */\nexport function listen_to_event_and_reset_event(element, event, handler, on_reset = handler) {\n\telement.addEventListener(event, () => without_reactive_context(handler));\n\t// @ts-expect-error\n\tconst prev = element.__on_r;\n\tif (prev) {\n\t\t// special case for checkbox that can have multiple binds (group & checked)\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => {\n\t\t\tprev();\n\t\t\ton_reset(true);\n\t\t};\n\t} else {\n\t\t// @ts-expect-error\n\t\telement.__on_r = () => on_reset(true);\n\t}\n\n\tadd_form_reset_listener();\n}\n", "/** @import { ComponentContext, ComponentContextLegacy, Derived, Effect, TemplateNode, TransitionManager } from '#client' */\nimport {\n\tis_dirty,\n\tactive_effect,\n\tactive_reaction,\n\tupdate_effect,\n\tget,\n\tis_destroying_effect,\n\tremove_reactions,\n\tset_active_reaction,\n\tset_is_destroying_effect,\n\tset_signal_status,\n\tuntrack,\n\tuntracking\n} from '../runtime.js';\nimport {\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tRENDER_EFFECT,\n\tEFFECT,\n\tDESTROYED,\n\tINERT,\n\tEFFECT_RAN,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tEFFECT_TRANSPARENT,\n\tDERIVED,\n\tCLEAN,\n\tEAGER_EFFECT,\n\tHEAD_EFFECT,\n\tMAYBE_DIRTY,\n\tEFFECT_PRESERVED,\n\tSTALE_REACTION,\n\tUSER_EFFECT,\n\tASYNC,\n\tCONNECTED\n} from '#client/constants';\nimport * as e from '../errors.js';\nimport { DEV } from 'esm-env';\nimport { define_property } from '../../shared/utils.js';\nimport { get_next_sibling } from '../dom/operations.js';\nimport { component_context, dev_current_component_function, dev_stack } from '../context.js';\nimport { Batch, current_batch, schedule_effect } from './batch.js';\nimport { flatten } from './async.js';\nimport { without_reactive_context } from '../dom/elements/bindings/shared.js';\n\n/**\n * @param {'$effect' | '$effect.pre' | '$inspect'} rune\n */\nexport function validate_effect(rune) {\n\tif (active_effect === null) {\n\t\tif (active_reaction === null) {\n\t\t\te.effect_orphan(rune);\n\t\t}\n\n\t\te.effect_in_unowned_derived();\n\t}\n\n\tif (is_destroying_effect) {\n\t\te.effect_in_teardown(rune);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {Effect} parent_effect\n */\nfunction push_effect(effect, parent_effect) {\n\tvar parent_last = parent_effect.last;\n\tif (parent_last === null) {\n\t\tparent_effect.last = parent_effect.first = effect;\n\t} else {\n\t\tparent_last.next = effect;\n\t\teffect.prev = parent_last;\n\t\tparent_effect.last = effect;\n\t}\n}\n\n/**\n * @param {number} type\n * @param {null | (() => void | (() => void))} fn\n * @param {boolean} sync\n * @returns {Effect}\n */\nfunction create_effect(type, fn, sync) {\n\tvar parent = active_effect;\n\n\tif (DEV) {\n\t\t// Ensure the parent is never an inspect effect\n\t\twhile (parent !== null && (parent.f & EAGER_EFFECT) !== 0) {\n\t\t\tparent = parent.parent;\n\t\t}\n\t}\n\n\tif (parent !== null && (parent.f & INERT) !== 0) {\n\t\ttype |= INERT;\n\t}\n\n\t/** @type {Effect} */\n\tvar effect = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\tnodes_start: null,\n\t\tnodes_end: null,\n\t\tf: type | DIRTY | CONNECTED,\n\t\tfirst: null,\n\t\tfn,\n\t\tlast: null,\n\t\tnext: null,\n\t\tparent,\n\t\tb: parent && parent.b,\n\t\tprev: null,\n\t\tteardown: null,\n\t\ttransitions: null,\n\t\twv: 0,\n\t\tac: null\n\t};\n\n\tif (DEV) {\n\t\teffect.component_function = dev_current_component_function;\n\t}\n\n\tif (sync) {\n\t\ttry {\n\t\t\tupdate_effect(effect);\n\t\t\teffect.f |= EFFECT_RAN;\n\t\t} catch (e) {\n\t\t\tdestroy_effect(effect);\n\t\t\tthrow e;\n\t\t}\n\t} else if (fn !== null) {\n\t\tschedule_effect(effect);\n\t}\n\n\t/** @type {Effect | null} */\n\tvar e = effect;\n\n\t// if an effect has already ran and doesn't need to be kept in the tree\n\t// (because it won't re-run, has no DOM, and has no teardown etc)\n\t// then we skip it and go to its child (if any)\n\tif (\n\t\tsync &&\n\t\te.deps === null &&\n\t\te.teardown === null &&\n\t\te.nodes_start === null &&\n\t\te.first === e.last && // either `null`, or a singular child\n\t\t(e.f & EFFECT_PRESERVED) === 0\n\t) {\n\t\te = e.first;\n\t\tif ((type & BLOCK_EFFECT) !== 0 && (type & EFFECT_TRANSPARENT) !== 0 && e !== null) {\n\t\t\te.f |= EFFECT_TRANSPARENT;\n\t\t}\n\t}\n\n\tif (e !== null) {\n\t\te.parent = parent;\n\n\t\tif (parent !== null) {\n\t\t\tpush_effect(e, parent);\n\t\t}\n\n\t\t// if we're in a derived, add the effect there too\n\t\tif (\n\t\t\tactive_reaction !== null &&\n\t\t\t(active_reaction.f & DERIVED) !== 0 &&\n\t\t\t(type & ROOT_EFFECT) === 0\n\t\t) {\n\t\t\tvar derived = /** @type {Derived} */ (active_reaction);\n\t\t\t(derived.effects ??= []).push(e);\n\t\t}\n\t}\n\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect.tracking()`\n * @returns {boolean}\n */\nexport function effect_tracking() {\n\treturn active_reaction !== null && !untracking;\n}\n\n/**\n * @param {() => void} fn\n */\nexport function teardown(fn) {\n\tconst effect = create_effect(RENDER_EFFECT, null, false);\n\tset_signal_status(effect, CLEAN);\n\teffect.teardown = fn;\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect(...)`\n * @param {() => void | (() => void)} fn\n */\nexport function user_effect(fn) {\n\tvalidate_effect('$effect');\n\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect'\n\t\t});\n\t}\n\n\t// Non-nested `$effect(...)` in a component should be deferred\n\t// until the component is mounted\n\tvar flags = /** @type {Effect} */ (active_effect).f;\n\tvar defer = !active_reaction && (flags & BRANCH_EFFECT) !== 0 && (flags & EFFECT_RAN) === 0;\n\n\tif (defer) {\n\t\t// Top-level `$effect(...)` in an unmounted component — defer until mount\n\t\tvar context = /** @type {ComponentContext} */ (component_context);\n\t\t(context.e ??= []).push(fn);\n\t} else {\n\t\t// Everything else — create immediately\n\t\treturn create_user_effect(fn);\n\t}\n}\n\n/**\n * @param {() => void | (() => void)} fn\n */\nexport function create_user_effect(fn) {\n\treturn create_effect(EFFECT | USER_EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$effect.pre(...)`\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function user_pre_effect(fn) {\n\tvalidate_effect('$effect.pre');\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect.pre'\n\t\t});\n\t}\n\treturn create_effect(RENDER_EFFECT | USER_EFFECT, fn, true);\n}\n\n/** @param {() => void | (() => void)} fn */\nexport function eager_effect(fn) {\n\treturn create_effect(EAGER_EFFECT, fn, true);\n}\n\n/**\n * Internal representation of `$effect.root(...)`\n * @param {() => void | (() => void)} fn\n * @returns {() => void}\n */\nexport function effect_root(fn) {\n\tBatch.ensure();\n\tconst effect = create_effect(ROOT_EFFECT | EFFECT_PRESERVED, fn, true);\n\n\treturn () => {\n\t\tdestroy_effect(effect);\n\t};\n}\n\n/**\n * An effect root whose children can transition out\n * @param {() => void} fn\n * @returns {(options?: { outro?: boolean }) => Promise<void>}\n */\nexport function component_root(fn) {\n\tBatch.ensure();\n\tconst effect = create_effect(ROOT_EFFECT | EFFECT_PRESERVED, fn, true);\n\n\treturn (options = {}) => {\n\t\treturn new Promise((fulfil) => {\n\t\t\tif (options.outro) {\n\t\t\t\tpause_effect(effect, () => {\n\t\t\t\t\tdestroy_effect(effect);\n\t\t\t\t\tfulfil(undefined);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tdestroy_effect(effect);\n\t\t\t\tfulfil(undefined);\n\t\t\t}\n\t\t});\n\t};\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function effect(fn) {\n\treturn create_effect(EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$: ..`\n * @param {() => any} deps\n * @param {() => void | (() => void)} fn\n */\nexport function legacy_pre_effect(deps, fn) {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\t/** @type {{ effect: null | Effect, ran: boolean, deps: () => any }} */\n\tvar token = { effect: null, ran: false, deps };\n\n\tcontext.l.$.push(token);\n\n\ttoken.effect = render_effect(() => {\n\t\tdeps();\n\n\t\t// If this legacy pre effect has already run before the end of the reset, then\n\t\t// bail out to emulate the same behavior.\n\t\tif (token.ran) return;\n\n\t\ttoken.ran = true;\n\t\tuntrack(fn);\n\t});\n}\n\nexport function legacy_pre_effect_reset() {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\trender_effect(() => {\n\t\t// Run dirty `$:` statements\n\t\tfor (var token of context.l.$) {\n\t\t\ttoken.deps();\n\n\t\t\tvar effect = token.effect;\n\n\t\t\t// If the effect is CLEAN, then make it MAYBE_DIRTY. This ensures we traverse through\n\t\t\t// the effects dependencies and correctly ensure each dependency is up-to-date.\n\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t}\n\n\t\t\tif (is_dirty(effect)) {\n\t\t\t\tupdate_effect(effect);\n\t\t\t}\n\n\t\t\ttoken.ran = false;\n\t\t}\n\t});\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function async_effect(fn) {\n\treturn create_effect(ASYNC | EFFECT_PRESERVED, fn, true);\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function render_effect(fn, flags = 0) {\n\treturn create_effect(RENDER_EFFECT | flags, fn, true);\n}\n\n/**\n * @param {(...expressions: any) => void | (() => void)} fn\n * @param {Array<() => any>} sync\n * @param {Array<() => Promise<any>>} async\n * @param {Array<Promise<void>>} blockers\n */\nexport function template_effect(fn, sync = [], async = [], blockers = []) {\n\tflatten(blockers, sync, async, (values) => {\n\t\tcreate_effect(RENDER_EFFECT, () => fn(...values.map(get)), true);\n\t});\n}\n\n/**\n * Like `template_effect`, but with an effect which is deferred until the batch commits\n * @param {(...expressions: any) => void | (() => void)} fn\n * @param {Array<() => any>} sync\n * @param {Array<() => Promise<any>>} async\n * @param {Array<Promise<void>>} blockers\n */\nexport function deferred_template_effect(fn, sync = [], async = [], blockers = []) {\n\tvar batch = /** @type {Batch} */ (current_batch);\n\tvar is_async = async.length > 0 || blockers.length > 0;\n\n\tif (is_async) batch.increment(true);\n\n\tflatten(blockers, sync, async, (values) => {\n\t\tcreate_effect(EFFECT, () => fn(...values.map(get)), false);\n\t\tif (is_async) batch.decrement(true);\n\t});\n}\n\n/**\n * @param {(() => void)} fn\n * @param {number} flags\n */\nexport function block(fn, flags = 0) {\n\tvar effect = create_effect(BLOCK_EFFECT | flags, fn, true);\n\tif (DEV) {\n\t\teffect.dev_stack = dev_stack;\n\t}\n\treturn effect;\n}\n\n/**\n * @param {(() => void)} fn\n */\nexport function branch(fn) {\n\treturn create_effect(BRANCH_EFFECT | EFFECT_PRESERVED, fn, true);\n}\n\n/**\n * @param {Effect} effect\n */\nexport function execute_effect_teardown(effect) {\n\tvar teardown = effect.teardown;\n\tif (teardown !== null) {\n\t\tconst previously_destroying_effect = is_destroying_effect;\n\t\tconst previous_reaction = active_reaction;\n\t\tset_is_destroying_effect(true);\n\t\tset_active_reaction(null);\n\t\ttry {\n\t\t\tteardown.call(null);\n\t\t} finally {\n\t\t\tset_is_destroying_effect(previously_destroying_effect);\n\t\t\tset_active_reaction(previous_reaction);\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @param {boolean} remove_dom\n * @returns {void}\n */\nexport function destroy_effect_children(signal, remove_dom = false) {\n\tvar effect = signal.first;\n\tsignal.first = signal.last = null;\n\n\twhile (effect !== null) {\n\t\tconst controller = effect.ac;\n\n\t\tif (controller !== null) {\n\t\t\twithout_reactive_context(() => {\n\t\t\t\tcontroller.abort(STALE_REACTION);\n\t\t\t});\n\t\t}\n\n\t\tvar next = effect.next;\n\n\t\tif ((effect.f & ROOT_EFFECT) !== 0) {\n\t\t\t// this is now an independent root\n\t\t\teffect.parent = null;\n\t\t} else {\n\t\t\tdestroy_effect(effect, remove_dom);\n\t\t}\n\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function destroy_block_effect_children(signal) {\n\tvar effect = signal.first;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\t\tif ((effect.f & BRANCH_EFFECT) === 0) {\n\t\t\tdestroy_effect(effect);\n\t\t}\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} [remove_dom]\n * @returns {void}\n */\nexport function destroy_effect(effect, remove_dom = true) {\n\tvar removed = false;\n\n\tif (\n\t\t(remove_dom || (effect.f & HEAD_EFFECT) !== 0) &&\n\t\teffect.nodes_start !== null &&\n\t\teffect.nodes_end !== null\n\t) {\n\t\tremove_effect_dom(effect.nodes_start, /** @type {TemplateNode} */ (effect.nodes_end));\n\t\tremoved = true;\n\t}\n\n\tdestroy_effect_children(effect, remove_dom && !removed);\n\tremove_reactions(effect, 0);\n\tset_signal_status(effect, DESTROYED);\n\n\tvar transitions = effect.transitions;\n\n\tif (transitions !== null) {\n\t\tfor (const transition of transitions) {\n\t\t\ttransition.stop();\n\t\t}\n\t}\n\n\texecute_effect_teardown(effect);\n\n\tvar parent = effect.parent;\n\n\t// If the parent doesn't have any children, then skip this work altogether\n\tif (parent !== null && parent.first !== null) {\n\t\tunlink_effect(effect);\n\t}\n\n\tif (DEV) {\n\t\teffect.component_function = null;\n\t}\n\n\t// `first` and `child` are nulled out in destroy_effect_children\n\t// we don't null out `parent` so that error propagation can work correctly\n\teffect.next =\n\t\teffect.prev =\n\t\teffect.teardown =\n\t\teffect.ctx =\n\t\teffect.deps =\n\t\teffect.fn =\n\t\teffect.nodes_start =\n\t\teffect.nodes_end =\n\t\teffect.ac =\n\t\t\tnull;\n}\n\n/**\n *\n * @param {TemplateNode | null} node\n * @param {TemplateNode} end\n */\nexport function remove_effect_dom(node, end) {\n\twhile (node !== null) {\n\t\t/** @type {TemplateNode | null} */\n\t\tvar next = node === end ? null : /** @type {TemplateNode} */ (get_next_sibling(node));\n\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n * Detach an effect from the effect tree, freeing up memory and\n * reducing the amount of work that happens on subsequent traversals\n * @param {Effect} effect\n */\nexport function unlink_effect(effect) {\n\tvar parent = effect.parent;\n\tvar prev = effect.prev;\n\tvar next = effect.next;\n\n\tif (prev !== null) prev.next = next;\n\tif (next !== null) next.prev = prev;\n\n\tif (parent !== null) {\n\t\tif (parent.first === effect) parent.first = next;\n\t\tif (parent.last === effect) parent.last = prev;\n\t}\n}\n\n/**\n * When a block effect is removed, we don't immediately destroy it or yank it\n * out of the DOM, because it might have transitions. Instead, we 'pause' it.\n * It stays around (in memory, and in the DOM) until outro transitions have\n * completed, and if the state change is reversed then we _resume_ it.\n * A paused effect does not update, and the DOM subtree becomes inert.\n * @param {Effect} effect\n * @param {() => void} [callback]\n * @param {boolean} [destroy]\n */\nexport function pause_effect(effect, callback, destroy = true) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\n\tpause_children(effect, transitions, true);\n\n\trun_out_transitions(transitions, () => {\n\t\tif (destroy) destroy_effect(effect);\n\t\tif (callback) callback();\n\t});\n}\n\n/**\n * @param {TransitionManager[]} transitions\n * @param {() => void} fn\n */\nexport function run_out_transitions(transitions, fn) {\n\tvar remaining = transitions.length;\n\tif (remaining > 0) {\n\t\tvar check = () => --remaining || fn();\n\t\tfor (var transition of transitions) {\n\t\t\ttransition.out(check);\n\t\t}\n\t} else {\n\t\tfn();\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {TransitionManager[]} transitions\n * @param {boolean} local\n */\nexport function pause_children(effect, transitions, local) {\n\tif ((effect.f & INERT) !== 0) return;\n\teffect.f ^= INERT;\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransitions.push(transition);\n\t\t\t}\n\t\t}\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent =\n\t\t\t(child.f & EFFECT_TRANSPARENT) !== 0 ||\n\t\t\t// If this is a branch effect without a block effect parent,\n\t\t\t// it means the parent block effect was pruned. In that case,\n\t\t\t// transparency information was transferred to the branch effect.\n\t\t\t((child.f & BRANCH_EFFECT) !== 0 && (effect.f & BLOCK_EFFECT) !== 0);\n\t\t// TODO we don't need to call pause_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tpause_children(child, transitions, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n}\n\n/**\n * The opposite of `pause_effect`. We call this if (for example)\n * `x` becomes falsy then truthy: `{#if x}...{/if}`\n * @param {Effect} effect\n */\nexport function resume_effect(effect) {\n\tresume_children(effect, true);\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} local\n */\nfunction resume_children(effect, local) {\n\tif ((effect.f & INERT) === 0) return;\n\teffect.f ^= INERT;\n\n\t// If a dependency of this effect changed while it was paused,\n\t// schedule the effect to update. we don't use `is_dirty`\n\t// here because we don't want to eagerly recompute a derived like\n\t// `{#if foo}{foo.bar()}{/if}` if `foo` is now `undefined\n\tif ((effect.f & CLEAN) === 0) {\n\t\tset_signal_status(effect, DIRTY);\n\t\tschedule_effect(effect);\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call resume_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tresume_children(child, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransition.in();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport function aborted(effect = /** @type {Effect} */ (active_effect)) {\n\treturn (effect.f & DESTROYED) !== 0;\n}\n\n/**\n * @param {Effect} effect\n * @param {DocumentFragment} fragment\n */\nexport function move_effect(effect, fragment) {\n\tvar node = effect.nodes_start;\n\tvar end = effect.nodes_end;\n\n\twhile (node !== null) {\n\t\t/** @type {TemplateNode | null} */\n\t\tvar next = node === end ? null : /** @type {TemplateNode} */ (get_next_sibling(node));\n\n\t\tfragment.append(node);\n\t\tnode = next;\n\t}\n}\n", "/** @import { Value } from '#client' */\nimport { internal_set } from './reactivity/sources.js';\nimport { untrack } from './runtime.js';\n\n/**\n * @type {Set<Value> | null}\n * @deprecated\n */\nexport let captured_signals = null;\n\n/**\n * Capture an array of all the signals that are read when `fn` is called\n * @template T\n * @param {() => T} fn\n */\nfunction capture_signals(fn) {\n\tvar previous_captured_signals = captured_signals;\n\n\ttry {\n\t\tcaptured_signals = new Set();\n\n\t\tuntrack(fn);\n\n\t\tif (previous_captured_signals !== null) {\n\t\t\tfor (var signal of captured_signals) {\n\t\t\t\tprevious_captured_signals.add(signal);\n\t\t\t}\n\t\t}\n\n\t\treturn captured_signals;\n\t} finally {\n\t\tcaptured_signals = previous_captured_signals;\n\t}\n}\n\n/**\n * Invokes a function and captures all signals that are read during the invocation,\n * then invalidates them.\n * @param {() => any} fn\n * @deprecated\n */\nexport function invalidate_inner_signals(fn) {\n\tfor (var signal of capture_signals(fn)) {\n\t\tinternal_set(signal, signal.v);\n\t}\n}\n", "/** @import { Derived, Effect, Reaction, Signal, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport { get_descriptors, get_prototype_of, index_of } from '../shared/utils.js';\nimport {\n\tdestroy_block_effect_children,\n\tdestroy_effect_children,\n\teffect_tracking,\n\texecute_effect_teardown\n} from './reactivity/effects.js';\nimport {\n\tDIRTY,\n\tMAYBE_DIRTY,\n\tCLEAN,\n\tDERIVED,\n\tDESTROYED,\n\tBRANCH_EFFECT,\n\tSTATE_SYMBOL,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tCONNECTED,\n\tREACTION_IS_UPDATING,\n\tSTALE_REACTION,\n\tERROR_VALUE,\n\tWAS_MARKED\n} from './constants.js';\nimport { old_values } from './reactivity/sources.js';\nimport {\n\tdestroy_derived_effects,\n\texecute_derived,\n\tcurrent_async_effect,\n\trecent_async_deriveds,\n\tupdate_derived\n} from './reactivity/deriveds.js';\nimport { async_mode_flag, tracing_mode_flag } from '../flags/index.js';\nimport { tracing_expressions, get_stack } from './dev/tracing.js';\nimport {\n\tcomponent_context,\n\tdev_current_component_function,\n\tdev_stack,\n\tis_runes,\n\tset_component_context,\n\tset_dev_current_component_function,\n\tset_dev_stack\n} from './context.js';\nimport * as w from './warnings.js';\nimport { Batch, batch_values, flushSync, schedule_effect } from './reactivity/batch.js';\nimport { handle_error } from './error-handling.js';\nimport { UNINITIALIZED } from '../../constants.js';\nimport { captured_signals } from './legacy.js';\nimport { without_reactive_context } from './dom/elements/bindings/shared.js';\n\nexport let is_updating_effect = false;\n\n/** @param {boolean} value */\nexport function set_is_updating_effect(value) {\n\tis_updating_effect = value;\n}\n\nexport let is_destroying_effect = false;\n\n/** @param {boolean} value */\nexport function set_is_destroying_effect(value) {\n\tis_destroying_effect = value;\n}\n\n/** @type {null | Reaction} */\nexport let active_reaction = null;\n\nexport let untracking = false;\n\n/** @param {null | Reaction} reaction */\nexport function set_active_reaction(reaction) {\n\tactive_reaction = reaction;\n}\n\n/** @type {null | Effect} */\nexport let active_effect = null;\n\n/** @param {null | Effect} effect */\nexport function set_active_effect(effect) {\n\tactive_effect = effect;\n}\n\n/**\n * When sources are created within a reaction, reading and writing\n * them within that reaction should not cause a re-run\n * @type {null | Source[]}\n */\nexport let current_sources = null;\n\n/** @param {Value} value */\nexport function push_reaction_value(value) {\n\tif (active_reaction !== null && (!async_mode_flag || (active_reaction.f & DERIVED) !== 0)) {\n\t\tif (current_sources === null) {\n\t\t\tcurrent_sources = [value];\n\t\t} else {\n\t\t\tcurrent_sources.push(value);\n\t\t}\n\t}\n}\n\n/**\n * The dependencies of the reaction that is currently being executed. In many cases,\n * the dependencies are unchanged between runs, and so this will be `null` unless\n * and until a new dependency is accessed — we track this via `skipped_deps`\n * @type {null | Value[]}\n */\nlet new_deps = null;\n\nlet skipped_deps = 0;\n\n/**\n * Tracks writes that the effect it's executed in doesn't listen to yet,\n * so that the dependency can be added to the effect later on if it then reads it\n * @type {null | Source[]}\n */\nexport let untracked_writes = null;\n\n/** @param {null | Source[]} value */\nexport function set_untracked_writes(value) {\n\tuntracked_writes = value;\n}\n\n/**\n * @type {number} Used by sources and deriveds for handling updates.\n * Version starts from 1 so that unowned deriveds differentiate between a created effect and a run one for tracing\n **/\nexport let write_version = 1;\n\n/** @type {number} Used to version each read of a source of derived to avoid duplicating depedencies inside a reaction */\nlet read_version = 0;\n\nexport let update_version = read_version;\n\n/** @param {number} value */\nexport function set_update_version(value) {\n\tupdate_version = value;\n}\n\nexport function increment_write_version() {\n\treturn ++write_version;\n}\n\n/**\n * Determines whether a derived or effect is dirty.\n * If it is MAYBE_DIRTY, will set the status to CLEAN\n * @param {Reaction} reaction\n * @returns {boolean}\n */\nexport function is_dirty(reaction) {\n\tvar flags = reaction.f;\n\n\tif ((flags & DIRTY) !== 0) {\n\t\treturn true;\n\t}\n\n\tif (flags & DERIVED) {\n\t\treaction.f &= ~WAS_MARKED;\n\t}\n\n\tif ((flags & MAYBE_DIRTY) !== 0) {\n\t\tvar dependencies = reaction.deps;\n\n\t\tif (dependencies !== null) {\n\t\t\tvar length = dependencies.length;\n\n\t\t\tfor (var i = 0; i < length; i++) {\n\t\t\t\tvar dependency = dependencies[i];\n\n\t\t\t\tif (is_dirty(/** @type {Derived} */ (dependency))) {\n\t\t\t\t\tupdate_derived(/** @type {Derived} */ (dependency));\n\t\t\t\t}\n\n\t\t\t\tif (dependency.wv > reaction.wv) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (\n\t\t\t(flags & CONNECTED) !== 0 &&\n\t\t\t// During time traveling we don't want to reset the status so that\n\t\t\t// traversal of the graph in the other batches still happens\n\t\t\tbatch_values === null\n\t\t) {\n\t\t\tset_signal_status(reaction, CLEAN);\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * @param {Value} signal\n * @param {Effect} effect\n * @param {boolean} [root]\n */\nfunction schedule_possible_effect_self_invalidation(signal, effect, root = true) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tif (!async_mode_flag && current_sources?.includes(signal)) {\n\t\treturn;\n\t}\n\n\tfor (var i = 0; i < reactions.length; i++) {\n\t\tvar reaction = reactions[i];\n\n\t\tif ((reaction.f & DERIVED) !== 0) {\n\t\t\tschedule_possible_effect_self_invalidation(/** @type {Derived} */ (reaction), effect, false);\n\t\t} else if (effect === reaction) {\n\t\t\tif (root) {\n\t\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\t} else if ((reaction.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(reaction, MAYBE_DIRTY);\n\t\t\t}\n\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n\n/** @param {Reaction} reaction */\nexport function update_reaction(reaction) {\n\tvar previous_deps = new_deps;\n\tvar previous_skipped_deps = skipped_deps;\n\tvar previous_untracked_writes = untracked_writes;\n\tvar previous_reaction = active_reaction;\n\tvar previous_sources = current_sources;\n\tvar previous_component_context = component_context;\n\tvar previous_untracking = untracking;\n\tvar previous_update_version = update_version;\n\n\tvar flags = reaction.f;\n\n\tnew_deps = /** @type {null | Value[]} */ (null);\n\tskipped_deps = 0;\n\tuntracked_writes = null;\n\tactive_reaction = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) === 0 ? reaction : null;\n\n\tcurrent_sources = null;\n\tset_component_context(reaction.ctx);\n\tuntracking = false;\n\tupdate_version = ++read_version;\n\n\tif (reaction.ac !== null) {\n\t\twithout_reactive_context(() => {\n\t\t\t/** @type {AbortController} */ (reaction.ac).abort(STALE_REACTION);\n\t\t});\n\n\t\treaction.ac = null;\n\t}\n\n\ttry {\n\t\treaction.f |= REACTION_IS_UPDATING;\n\t\tvar fn = /** @type {Function} */ (reaction.fn);\n\t\tvar result = fn();\n\t\tvar deps = reaction.deps;\n\n\t\tif (new_deps !== null) {\n\t\t\tvar i;\n\n\t\t\tremove_reactions(reaction, skipped_deps);\n\n\t\t\tif (deps !== null && skipped_deps > 0) {\n\t\t\t\tdeps.length = skipped_deps + new_deps.length;\n\t\t\t\tfor (i = 0; i < new_deps.length; i++) {\n\t\t\t\t\tdeps[skipped_deps + i] = new_deps[i];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treaction.deps = deps = new_deps;\n\t\t\t}\n\n\t\t\tif (is_updating_effect && effect_tracking() && (reaction.f & CONNECTED) !== 0) {\n\t\t\t\tfor (i = skipped_deps; i < deps.length; i++) {\n\t\t\t\t\t(deps[i].reactions ??= []).push(reaction);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (deps !== null && skipped_deps < deps.length) {\n\t\t\tremove_reactions(reaction, skipped_deps);\n\t\t\tdeps.length = skipped_deps;\n\t\t}\n\n\t\t// If we're inside an effect and we have untracked writes, then we need to\n\t\t// ensure that if any of those untracked writes result in re-invalidation\n\t\t// of the current effect, then that happens accordingly\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tuntracked_writes !== null &&\n\t\t\t!untracking &&\n\t\t\tdeps !== null &&\n\t\t\t(reaction.f & (DERIVED | MAYBE_DIRTY | DIRTY)) === 0\n\t\t) {\n\t\t\tfor (i = 0; i < /** @type {Source[]} */ (untracked_writes).length; i++) {\n\t\t\t\tschedule_possible_effect_self_invalidation(\n\t\t\t\t\tuntracked_writes[i],\n\t\t\t\t\t/** @type {Effect} */ (reaction)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t// If we are returning to an previous reaction then\n\t\t// we need to increment the read version to ensure that\n\t\t// any dependencies in this reaction aren't marked with\n\t\t// the same version\n\t\tif (previous_reaction !== null && previous_reaction !== reaction) {\n\t\t\tread_version++;\n\n\t\t\tif (untracked_writes !== null) {\n\t\t\t\tif (previous_untracked_writes === null) {\n\t\t\t\t\tprevious_untracked_writes = untracked_writes;\n\t\t\t\t} else {\n\t\t\t\t\tprevious_untracked_writes.push(.../** @type {Source[]} */ (untracked_writes));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif ((reaction.f & ERROR_VALUE) !== 0) {\n\t\t\treaction.f ^= ERROR_VALUE;\n\t\t}\n\n\t\treturn result;\n\t} catch (error) {\n\t\treturn handle_error(error);\n\t} finally {\n\t\treaction.f ^= REACTION_IS_UPDATING;\n\t\tnew_deps = previous_deps;\n\t\tskipped_deps = previous_skipped_deps;\n\t\tuntracked_writes = previous_untracked_writes;\n\t\tactive_reaction = previous_reaction;\n\t\tcurrent_sources = previous_sources;\n\t\tset_component_context(previous_component_context);\n\t\tuntracking = previous_untracking;\n\t\tupdate_version = previous_update_version;\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} signal\n * @param {Value<V>} dependency\n * @returns {void}\n */\nfunction remove_reaction(signal, dependency) {\n\tlet reactions = dependency.reactions;\n\tif (reactions !== null) {\n\t\tvar index = index_of.call(reactions, signal);\n\t\tif (index !== -1) {\n\t\t\tvar new_length = reactions.length - 1;\n\t\t\tif (new_length === 0) {\n\t\t\t\treactions = dependency.reactions = null;\n\t\t\t} else {\n\t\t\t\t// Swap with last element and then remove.\n\t\t\t\treactions[index] = reactions[new_length];\n\t\t\t\treactions.pop();\n\t\t\t}\n\t\t}\n\t}\n\n\t// If the derived has no reactions, then we can disconnect it from the graph,\n\t// allowing it to either reconnect in the future, or be GC'd by the VM.\n\tif (\n\t\treactions === null &&\n\t\t(dependency.f & DERIVED) !== 0 &&\n\t\t// Destroying a child effect while updating a parent effect can cause a dependency to appear\n\t\t// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`\n\t\t// allows us to skip the expensive work of disconnecting and immediately reconnecting it\n\t\t(new_deps === null || !new_deps.includes(dependency))\n\t) {\n\t\tset_signal_status(dependency, MAYBE_DIRTY);\n\t\t// If we are working with a derived that is owned by an effect, then mark it as being\n\t\t// disconnected and remove the mark flag, as it cannot be reliably removed otherwise\n\t\tif ((dependency.f & CONNECTED) !== 0) {\n\t\t\tdependency.f ^= CONNECTED;\n\t\t\tdependency.f &= ~WAS_MARKED;\n\t\t}\n\t\t// Disconnect any reactions owned by this reaction\n\t\tdestroy_derived_effects(/** @type {Derived} **/ (dependency));\n\t\tremove_reactions(/** @type {Derived} **/ (dependency), 0);\n\t}\n}\n\n/**\n * @param {Reaction} signal\n * @param {number} start_index\n * @returns {void}\n */\nexport function remove_reactions(signal, start_index) {\n\tvar dependencies = signal.deps;\n\tif (dependencies === null) return;\n\n\tfor (var i = start_index; i < dependencies.length; i++) {\n\t\tremove_reaction(signal, dependencies[i]);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @returns {void}\n */\nexport function update_effect(effect) {\n\tvar flags = effect.f;\n\n\tif ((flags & DESTROYED) !== 0) {\n\t\treturn;\n\t}\n\n\tset_signal_status(effect, CLEAN);\n\n\tvar previous_effect = active_effect;\n\tvar was_updating_effect = is_updating_effect;\n\n\tactive_effect = effect;\n\tis_updating_effect = true;\n\n\tif (DEV) {\n\t\tvar previous_component_fn = dev_current_component_function;\n\t\tset_dev_current_component_function(effect.component_function);\n\t\tvar previous_stack = /** @type {any} */ (dev_stack);\n\t\t// only block effects have a dev stack, keep the current one otherwise\n\t\tset_dev_stack(effect.dev_stack ?? dev_stack);\n\t}\n\n\ttry {\n\t\tif ((flags & BLOCK_EFFECT) !== 0) {\n\t\t\tdestroy_block_effect_children(effect);\n\t\t} else {\n\t\t\tdestroy_effect_children(effect);\n\t\t}\n\n\t\texecute_effect_teardown(effect);\n\t\tvar teardown = update_reaction(effect);\n\t\teffect.teardown = typeof teardown === 'function' ? teardown : null;\n\t\teffect.wv = write_version;\n\n\t\t// In DEV, increment versions of any sources that were written to during the effect,\n\t\t// so that they are correctly marked as dirty when the effect re-runs\n\t\tif (DEV && tracing_mode_flag && (effect.f & DIRTY) !== 0 && effect.deps !== null) {\n\t\t\tfor (var dep of effect.deps) {\n\t\t\t\tif (dep.set_during_effect) {\n\t\t\t\t\tdep.wv = increment_write_version();\n\t\t\t\t\tdep.set_during_effect = false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tis_updating_effect = was_updating_effect;\n\t\tactive_effect = previous_effect;\n\n\t\tif (DEV) {\n\t\t\tset_dev_current_component_function(previous_component_fn);\n\t\t\tset_dev_stack(previous_stack);\n\t\t}\n\t}\n}\n\n/**\n * Returns a promise that resolves once any pending state changes have been applied.\n * @returns {Promise<void>}\n */\nexport async function tick() {\n\tif (async_mode_flag) {\n\t\treturn new Promise((f) => {\n\t\t\t// Race them against each other - in almost all cases requestAnimationFrame will fire first,\n\t\t\t// but e.g. in case the window is not focused or a view transition happens, requestAnimationFrame\n\t\t\t// will be delayed and setTimeout helps us resolve fast enough in that case\n\t\t\trequestAnimationFrame(() => f());\n\t\t\tsetTimeout(() => f());\n\t\t});\n\t}\n\n\tawait Promise.resolve();\n\n\t// By calling flushSync we guarantee that any pending state changes are applied after one tick.\n\t// TODO look into whether we can make flushing subsequent updates synchronously in the future.\n\tflushSync();\n}\n\n/**\n * Returns a promise that resolves once any state changes, and asynchronous work resulting from them,\n * have resolved and the DOM has been updated\n * @returns {Promise<void>}\n * @since 5.36\n */\nexport function settled() {\n\treturn Batch.ensure().settled();\n}\n\n/**\n * @template V\n * @param {Value<V>} signal\n * @returns {V}\n */\nexport function get(signal) {\n\tvar flags = signal.f;\n\tvar is_derived = (flags & DERIVED) !== 0;\n\n\tcaptured_signals?.add(signal);\n\n\t// Register the dependency on the current reaction signal.\n\tif (active_reaction !== null && !untracking) {\n\t\t// if we're in a derived that is being read inside an _async_ derived,\n\t\t// it's possible that the effect was already destroyed. In this case,\n\t\t// we don't add the dependency, because that would create a memory leak\n\t\tvar destroyed = active_effect !== null && (active_effect.f & DESTROYED) !== 0;\n\n\t\tif (!destroyed && !current_sources?.includes(signal)) {\n\t\t\tvar deps = active_reaction.deps;\n\n\t\t\tif ((active_reaction.f & REACTION_IS_UPDATING) !== 0) {\n\t\t\t\t// we're in the effect init/update cycle\n\t\t\t\tif (signal.rv < read_version) {\n\t\t\t\t\tsignal.rv = read_version;\n\n\t\t\t\t\t// If the signal is accessing the same dependencies in the same\n\t\t\t\t\t// order as it did last time, increment `skipped_deps`\n\t\t\t\t\t// rather than updating `new_deps`, which creates GC cost\n\t\t\t\t\tif (new_deps === null && deps !== null && deps[skipped_deps] === signal) {\n\t\t\t\t\t\tskipped_deps++;\n\t\t\t\t\t} else if (new_deps === null) {\n\t\t\t\t\t\tnew_deps = [signal];\n\t\t\t\t\t} else if (!new_deps.includes(signal)) {\n\t\t\t\t\t\tnew_deps.push(signal);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// we're adding a dependency outside the init/update cycle\n\t\t\t\t// (i.e. after an `await`)\n\t\t\t\t(active_reaction.deps ??= []).push(signal);\n\n\t\t\t\tvar reactions = signal.reactions;\n\n\t\t\t\tif (reactions === null) {\n\t\t\t\t\tsignal.reactions = [active_reaction];\n\t\t\t\t} else if (!reactions.includes(active_reaction)) {\n\t\t\t\t\treactions.push(active_reaction);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tif (DEV) {\n\t\t// TODO reinstate this, but make it actually work\n\t\t// if (current_async_effect) {\n\t\t// \tvar tracking = (current_async_effect.f & REACTION_IS_UPDATING) !== 0;\n\t\t// \tvar was_read = current_async_effect.deps?.includes(signal);\n\n\t\t// \tif (!tracking && !untracking && !was_read) {\n\t\t// \t\tw.await_reactivity_loss(/** @type {string} */ (signal.label));\n\n\t\t// \t\tvar trace = get_stack('traced at');\n\t\t// \t\t// eslint-disable-next-line no-console\n\t\t// \t\tif (trace) console.warn(trace);\n\t\t// \t}\n\t\t// }\n\n\t\trecent_async_deriveds.delete(signal);\n\n\t\tif (\n\t\t\ttracing_mode_flag &&\n\t\t\t!untracking &&\n\t\t\ttracing_expressions !== null &&\n\t\t\tactive_reaction !== null &&\n\t\t\ttracing_expressions.reaction === active_reaction\n\t\t) {\n\t\t\t// Used when mapping state between special blocks like `each`\n\t\t\tif (signal.trace) {\n\t\t\t\tsignal.trace();\n\t\t\t} else {\n\t\t\t\tvar trace = get_stack('traced at');\n\n\t\t\t\tif (trace) {\n\t\t\t\t\tvar entry = tracing_expressions.entries.get(signal);\n\n\t\t\t\t\tif (entry === undefined) {\n\t\t\t\t\t\tentry = { traces: [] };\n\t\t\t\t\t\ttracing_expressions.entries.set(signal, entry);\n\t\t\t\t\t}\n\n\t\t\t\t\tvar last = entry.traces[entry.traces.length - 1];\n\n\t\t\t\t\t// traces can be duplicated, e.g. by `snapshot` invoking both\n\t\t\t\t\t// both `getOwnPropertyDescriptor` and `get` traps at once\n\t\t\t\t\tif (trace.stack !== last?.stack) {\n\t\t\t\t\t\tentry.traces.push(trace);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tif (is_destroying_effect) {\n\t\tif (old_values.has(signal)) {\n\t\t\treturn old_values.get(signal);\n\t\t}\n\n\t\tif (is_derived) {\n\t\t\tvar derived = /** @type {Derived} */ (signal);\n\n\t\t\tvar value = derived.v;\n\n\t\t\t// if the derived is dirty and has reactions, or depends on the values that just changed, re-execute\n\t\t\t// (a derived can be maybe_dirty due to the effect destroy removing its last reaction)\n\t\t\tif (\n\t\t\t\t((derived.f & CLEAN) === 0 && derived.reactions !== null) ||\n\t\t\t\tdepends_on_old_values(derived)\n\t\t\t) {\n\t\t\t\tvalue = execute_derived(derived);\n\t\t\t}\n\n\t\t\told_values.set(derived, value);\n\n\t\t\treturn value;\n\t\t}\n\t} else if (is_derived && !batch_values?.has(signal)) {\n\t\tderived = /** @type {Derived} */ (signal);\n\n\t\tif (is_dirty(derived)) {\n\t\t\tupdate_derived(derived);\n\t\t}\n\n\t\tif (is_updating_effect && effect_tracking() && (derived.f & CONNECTED) === 0) {\n\t\t\treconnect(derived);\n\t\t}\n\t}\n\n\tif (batch_values?.has(signal)) {\n\t\treturn batch_values.get(signal);\n\t}\n\n\tif ((signal.f & ERROR_VALUE) !== 0) {\n\t\tthrow signal.v;\n\t}\n\n\treturn signal.v;\n}\n\n/**\n * (Re)connect a disconnected derived, so that it is notified\n * of changes in `mark_reactions`\n * @param {Derived} derived\n */\nfunction reconnect(derived) {\n\tif (derived.deps === null) return;\n\n\tderived.f ^= CONNECTED;\n\n\tfor (const dep of derived.deps) {\n\t\t(dep.reactions ??= []).push(derived);\n\n\t\tif ((dep.f & DERIVED) !== 0 && (dep.f & CONNECTED) === 0) {\n\t\t\treconnect(/** @type {Derived} */ (dep));\n\t\t}\n\t}\n}\n\n/** @param {Derived} derived */\nfunction depends_on_old_values(derived) {\n\tif (derived.v === UNINITIALIZED) return true; // we don't know, so assume the worst\n\tif (derived.deps === null) return false;\n\n\tfor (const dep of derived.deps) {\n\t\tif (old_values.has(dep)) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif ((dep.f & DERIVED) !== 0 && depends_on_old_values(/** @type {Derived} */ (dep))) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * Like `get`, but checks for `undefined`. Used for `var` declarations because they can be accessed before being declared\n * @template V\n * @param {Value<V> | undefined} signal\n * @returns {V | undefined}\n */\nexport function safe_get(signal) {\n\treturn signal && get(signal);\n}\n\n/**\n * When used inside a [`$derived`](https://svelte.dev/docs/svelte/$derived) or [`$effect`](https://svelte.dev/docs/svelte/$effect),\n * any state read inside `fn` will not be treated as a dependency.\n *\n * ```ts\n * $effect(() => {\n *   // this will run when `data` changes, but not when `time` changes\n *   save(data, {\n *     timestamp: untrack(() => time)\n *   });\n * });\n * ```\n * @template T\n * @param {() => T} fn\n * @returns {T}\n */\nexport function untrack(fn) {\n\tvar previous_untracking = untracking;\n\ttry {\n\t\tuntracking = true;\n\t\treturn fn();\n\t} finally {\n\t\tuntracking = previous_untracking;\n\t}\n}\n\nconst STATUS_MASK = ~(DIRTY | MAYBE_DIRTY | CLEAN);\n\n/**\n * @param {Signal} signal\n * @param {number} status\n * @returns {void}\n */\nexport function set_signal_status(signal, status) {\n\tsignal.f = (signal.f & STATUS_MASK) | status;\n}\n\n/**\n * @param {Record<string | symbol, unknown>} obj\n * @param {Array<string | symbol>} keys\n * @returns {Record<string | symbol, unknown>}\n */\nexport function exclude_from_object(obj, keys) {\n\t/** @type {Record<string | symbol, unknown>} */\n\tvar result = {};\n\n\tfor (var key in obj) {\n\t\tif (!keys.includes(key)) {\n\t\t\tresult[key] = obj[key];\n\t\t}\n\t}\n\n\tfor (var symbol of Object.getOwnPropertySymbols(obj)) {\n\t\tif (Object.propertyIsEnumerable.call(obj, symbol) && !keys.includes(symbol)) {\n\t\t\tresult[symbol] = obj[symbol];\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Possibly traverse an object and read all its properties so that they're all reactive in case this is `$state`.\n * Does only check first level of an object for performance reasons (heuristic should be good for 99% of all cases).\n * @param {any} value\n * @returns {void}\n */\nexport function deep_read_state(value) {\n\tif (typeof value !== 'object' || !value || value instanceof EventTarget) {\n\t\treturn;\n\t}\n\n\tif (STATE_SYMBOL in value) {\n\t\tdeep_read(value);\n\t} else if (!Array.isArray(value)) {\n\t\tfor (let key in value) {\n\t\t\tconst prop = value[key];\n\t\t\tif (typeof prop === 'object' && prop && STATE_SYMBOL in prop) {\n\t\t\t\tdeep_read(prop);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Deeply traverse an object and read all its properties\n * so that they're all reactive in case this is `$state`\n * @param {any} value\n * @param {Set<any>} visited\n * @returns {void}\n */\nexport function deep_read(value, visited = new Set()) {\n\tif (\n\t\ttypeof value === 'object' &&\n\t\tvalue !== null &&\n\t\t// We don't want to traverse DOM elements\n\t\t!(value instanceof EventTarget) &&\n\t\t!visited.has(value)\n\t) {\n\t\tvisited.add(value);\n\t\t// When working with a possible SvelteDate, this\n\t\t// will ensure we capture changes to it.\n\t\tif (value instanceof Date) {\n\t\t\tvalue.getTime();\n\t\t}\n\t\tfor (let key in value) {\n\t\t\ttry {\n\t\t\t\tdeep_read(value[key], visited);\n\t\t\t} catch (e) {\n\t\t\t\t// continue\n\t\t\t}\n\t\t}\n\t\tconst proto = get_prototype_of(value);\n\t\tif (\n\t\t\tproto !== Object.prototype &&\n\t\t\tproto !== Array.prototype &&\n\t\t\tproto !== Map.prototype &&\n\t\t\tproto !== Set.prototype &&\n\t\t\tproto !== Date.prototype\n\t\t) {\n\t\t\tconst descriptors = get_descriptors(proto);\n\t\t\tfor (let key in descriptors) {\n\t\t\t\tconst get = descriptors[key].get;\n\t\t\t\tif (get) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tget.call(value);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// continue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/** @import { Source } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tget,\n\tactive_effect,\n\tupdate_version,\n\tactive_reaction,\n\tset_update_version,\n\tset_active_reaction\n} from './runtime.js';\nimport {\n\tarray_prototype,\n\tget_descriptor,\n\tget_prototype_of,\n\tis_array,\n\tobject_prototype\n} from '../shared/utils.js';\nimport {\n\tstate as source,\n\tset,\n\tincrement,\n\tflush_eager_effects,\n\tset_eager_effects_deferred\n} from './reactivity/sources.js';\nimport { PROXY_PATH_SYMBOL, STATE_SYMBOL } from '#client/constants';\nimport { UNINITIALIZED } from '../../constants.js';\nimport * as e from './errors.js';\nimport { get_stack, tag } from './dev/tracing.js';\nimport { tracing_mode_flag } from '../flags/index.js';\n\n// TODO move all regexes into shared module?\nconst regex_is_valid_identifier = /^[a-zA-Z_$][a-zA-Z_$0-9]*$/;\n\n/**\n * @template T\n * @param {T} value\n * @returns {T}\n */\nexport function proxy(value) {\n\t// if non-proxyable, or is already a proxy, return `value`\n\tif (typeof value !== 'object' || value === null || STATE_SYMBOL in value) {\n\t\treturn value;\n\t}\n\n\tconst prototype = get_prototype_of(value);\n\n\tif (prototype !== object_prototype && prototype !== array_prototype) {\n\t\treturn value;\n\t}\n\n\t/** @type {Map<any, Source<any>>} */\n\tvar sources = new Map();\n\tvar is_proxied_array = is_array(value);\n\tvar version = source(0);\n\n\tvar stack = DEV && tracing_mode_flag ? get_stack('created at') : null;\n\tvar parent_version = update_version;\n\n\t/**\n\t * Executes the proxy in the context of the reaction it was originally created in, if any\n\t * @template T\n\t * @param {() => T} fn\n\t */\n\tvar with_parent = (fn) => {\n\t\tif (update_version === parent_version) {\n\t\t\treturn fn();\n\t\t}\n\n\t\t// child source is being created after the initial proxy —\n\t\t// prevent it from being associated with the current reaction\n\t\tvar reaction = active_reaction;\n\t\tvar version = update_version;\n\n\t\tset_active_reaction(null);\n\t\tset_update_version(parent_version);\n\n\t\tvar result = fn();\n\n\t\tset_active_reaction(reaction);\n\t\tset_update_version(version);\n\n\t\treturn result;\n\t};\n\n\tif (is_proxied_array) {\n\t\t// We need to create the length source eagerly to ensure that\n\t\t// mutations to the array are properly synced with our proxy\n\t\tsources.set('length', source(/** @type {any[]} */ (value).length, stack));\n\t\tif (DEV) {\n\t\t\tvalue = /** @type {any} */ (inspectable_array(/** @type {any[]} */ (value)));\n\t\t}\n\t}\n\n\t/** Used in dev for $inspect.trace() */\n\tvar path = '';\n\tlet updating = false;\n\t/** @param {string} new_path */\n\tfunction update_path(new_path) {\n\t\tif (updating) return;\n\t\tupdating = true;\n\t\tpath = new_path;\n\n\t\ttag(version, `${path} version`);\n\n\t\t// rename all child sources and child proxies\n\t\tfor (const [prop, source] of sources) {\n\t\t\ttag(source, get_label(path, prop));\n\t\t}\n\t\tupdating = false;\n\t}\n\n\treturn new Proxy(/** @type {any} */ (value), {\n\t\tdefineProperty(_, prop, descriptor) {\n\t\t\tif (\n\t\t\t\t!('value' in descriptor) ||\n\t\t\t\tdescriptor.configurable === false ||\n\t\t\t\tdescriptor.enumerable === false ||\n\t\t\t\tdescriptor.writable === false\n\t\t\t) {\n\t\t\t\t// we disallow non-basic descriptors, because unless they are applied to the\n\t\t\t\t// target object — which we avoid, so that state can be forked — we will run\n\t\t\t\t// afoul of the various invariants\n\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/Proxy/getOwnPropertyDescriptor#invariants\n\t\t\t\te.state_descriptors_fixed();\n\t\t\t}\n\t\t\tvar s = sources.get(prop);\n\t\t\tif (s === undefined) {\n\t\t\t\ts = with_parent(() => {\n\t\t\t\t\tvar s = source(descriptor.value, stack);\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t\tif (DEV && typeof prop === 'string') {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t\treturn s;\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tset(s, descriptor.value, true);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tdeleteProperty(target, prop) {\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\tif (prop in target) {\n\t\t\t\t\tconst s = with_parent(() => source(UNINITIALIZED, stack));\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t\tincrement(version);\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tset(s, UNINITIALIZED);\n\t\t\t\tincrement(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tget(target, prop, receiver) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (DEV && prop === PROXY_PATH_SYMBOL) {\n\t\t\t\treturn update_path;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar exists = prop in target;\n\n\t\t\t// create a source, but only if it's an own property and not a prototype property\n\t\t\tif (s === undefined && (!exists || get_descriptor(target, prop)?.writable)) {\n\t\t\t\ts = with_parent(() => {\n\t\t\t\t\tvar p = proxy(exists ? target[prop] : UNINITIALIZED);\n\t\t\t\t\tvar s = source(p, stack);\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\n\t\t\t\t\treturn s;\n\t\t\t\t});\n\n\t\t\t\tsources.set(prop, s);\n\t\t\t}\n\n\t\t\tif (s !== undefined) {\n\t\t\t\tvar v = get(s);\n\t\t\t\treturn v === UNINITIALIZED ? undefined : v;\n\t\t\t}\n\n\t\t\treturn Reflect.get(target, prop, receiver);\n\t\t},\n\n\t\tgetOwnPropertyDescriptor(target, prop) {\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\tif (descriptor && 'value' in descriptor) {\n\t\t\t\tvar s = sources.get(prop);\n\t\t\t\tif (s) descriptor.value = get(s);\n\t\t\t} else if (descriptor === undefined) {\n\t\t\t\tvar source = sources.get(prop);\n\t\t\t\tvar value = source?.v;\n\n\t\t\t\tif (source !== undefined && value !== UNINITIALIZED) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\twritable: true\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn descriptor;\n\t\t},\n\n\t\thas(target, prop) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = (s !== undefined && s.v !== UNINITIALIZED) || Reflect.has(target, prop);\n\n\t\t\tif (\n\t\t\t\ts !== undefined ||\n\t\t\t\t(active_effect !== null && (!has || get_descriptor(target, prop)?.writable))\n\t\t\t) {\n\t\t\t\tif (s === undefined) {\n\t\t\t\t\ts = with_parent(() => {\n\t\t\t\t\t\tvar p = has ? proxy(target[prop]) : UNINITIALIZED;\n\t\t\t\t\t\tvar s = source(p, stack);\n\n\t\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn s;\n\t\t\t\t\t});\n\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\n\t\t\t\tvar value = get(s);\n\t\t\t\tif (value === UNINITIALIZED) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn has;\n\t\t},\n\n\t\tset(target, prop, value, receiver) {\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = prop in target;\n\n\t\t\t// variable.length = value -> clear all signals with index >= value\n\t\t\tif (is_proxied_array && prop === 'length') {\n\t\t\t\tfor (var i = value; i < /** @type {Source<number>} */ (s).v; i += 1) {\n\t\t\t\t\tvar other_s = sources.get(i + '');\n\t\t\t\t\tif (other_s !== undefined) {\n\t\t\t\t\t\tset(other_s, UNINITIALIZED);\n\t\t\t\t\t} else if (i in target) {\n\t\t\t\t\t\t// If the item exists in the original, we need to create an uninitialized source,\n\t\t\t\t\t\t// else a later read of the property would result in a source being created with\n\t\t\t\t\t\t// the value of the original item at that index.\n\t\t\t\t\t\tother_s = with_parent(() => source(UNINITIALIZED, stack));\n\t\t\t\t\t\tsources.set(i + '', other_s);\n\n\t\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\t\ttag(other_s, get_label(path, i));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we haven't yet created a source for this property, we need to ensure\n\t\t\t// we do so otherwise if we read it later, then the write won't be tracked and\n\t\t\t// the heuristics of effects will be different vs if we had read the proxied\n\t\t\t// object property before writing to that property.\n\t\t\tif (s === undefined) {\n\t\t\t\tif (!has || get_descriptor(target, prop)?.writable) {\n\t\t\t\t\ts = with_parent(() => source(undefined, stack));\n\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\ttag(s, get_label(path, prop));\n\t\t\t\t\t}\n\t\t\t\t\tset(s, proxy(value));\n\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thas = s.v !== UNINITIALIZED;\n\n\t\t\t\tvar p = with_parent(() => proxy(value));\n\t\t\t\tset(s, p);\n\t\t\t}\n\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\t// Set the new value before updating any signals so that any listeners get the new value\n\t\t\tif (descriptor?.set) {\n\t\t\t\tdescriptor.set.call(receiver, value);\n\t\t\t}\n\n\t\t\tif (!has) {\n\t\t\t\t// If we have mutated an array directly, we might need to\n\t\t\t\t// signal that length has also changed. Do it before updating metadata\n\t\t\t\t// to ensure that iterating over the array as a result of a metadata update\n\t\t\t\t// will not cause the length to be out of sync.\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n >= ls.v) {\n\t\t\t\t\t\tset(ls, n + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tincrement(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\townKeys(target) {\n\t\t\tget(version);\n\n\t\t\tvar own_keys = Reflect.ownKeys(target).filter((key) => {\n\t\t\t\tvar source = sources.get(key);\n\t\t\t\treturn source === undefined || source.v !== UNINITIALIZED;\n\t\t\t});\n\n\t\t\tfor (var [key, source] of sources) {\n\t\t\t\tif (source.v !== UNINITIALIZED && !(key in target)) {\n\t\t\t\t\town_keys.push(key);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn own_keys;\n\t\t},\n\n\t\tsetPrototypeOf() {\n\t\t\te.state_prototype_fixed();\n\t\t}\n\t});\n}\n\n/**\n * @param {string} path\n * @param {string | symbol} prop\n */\nfunction get_label(path, prop) {\n\tif (typeof prop === 'symbol') return `${path}[Symbol(${prop.description ?? ''})]`;\n\tif (regex_is_valid_identifier.test(prop)) return `${path}.${prop}`;\n\treturn /^\\d+$/.test(prop) ? `${path}[${prop}]` : `${path}['${prop}']`;\n}\n\n/**\n * @param {any} value\n */\nexport function get_proxied_value(value) {\n\ttry {\n\t\tif (value !== null && typeof value === 'object' && STATE_SYMBOL in value) {\n\t\t\treturn value[STATE_SYMBOL];\n\t\t}\n\t} catch {\n\t\t// the above if check can throw an error if the value in question\n\t\t// is the contentWindow of an iframe on another domain, in which\n\t\t// case we want to just return the value (because it's definitely\n\t\t// not a proxied value) so we don't break any JavaScript interacting\n\t\t// with that iframe (such as various payment companies client side\n\t\t// JavaScript libraries interacting with their iframes on the same\n\t\t// domain)\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n */\nexport function is(a, b) {\n\treturn Object.is(get_proxied_value(a), get_proxied_value(b));\n}\n\nconst ARRAY_MUTATING_METHODS = new Set([\n\t'copyWithin',\n\t'fill',\n\t'pop',\n\t'push',\n\t'reverse',\n\t'shift',\n\t'sort',\n\t'splice',\n\t'unshift'\n]);\n\n/**\n * Wrap array mutating methods so $inspect is triggered only once and\n * to prevent logging an array in intermediate state (e.g. with an empty slot)\n * @param {any[]} array\n */\nfunction inspectable_array(array) {\n\treturn new Proxy(array, {\n\t\tget(target, prop, receiver) {\n\t\t\tvar value = Reflect.get(target, prop, receiver);\n\t\t\tif (!ARRAY_MUTATING_METHODS.has(/** @type {string} */ (prop))) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * @this {any[]}\n\t\t\t * @param {any[]} args\n\t\t\t */\n\t\t\treturn function (...args) {\n\t\t\t\tset_eager_effects_deferred();\n\t\t\t\tvar result = value.apply(this, args);\n\t\t\t\tflush_eager_effects();\n\t\t\t\treturn result;\n\t\t\t};\n\t\t}\n\t});\n}\n", "import * as w from '../warnings.js';\nimport { get_proxied_value } from '../proxy.js';\n\nexport function init_array_prototype_warnings() {\n\tconst array_prototype = Array.prototype;\n\t// The REPL ends up here over and over, and this prevents it from adding more and more patches\n\t// of the same kind to the prototype, which would slow down everything over time.\n\t// @ts-expect-error\n\tconst cleanup = Array.__svelte_cleanup;\n\tif (cleanup) {\n\t\tcleanup();\n\t}\n\n\tconst { indexOf, lastIndexOf, includes } = array_prototype;\n\n\tarray_prototype.indexOf = function (item, from_index) {\n\t\tconst index = indexOf.call(this, item, from_index);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = from_index ?? 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.indexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.lastIndexOf = function (item, from_index) {\n\t\t// we need to specify this.length - 1 because it's probably using something like\n\t\t// `arguments` inside so passing undefined is different from not passing anything\n\t\tconst index = lastIndexOf.call(this, item, from_index ?? this.length - 1);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = 0; i <= (from_index ?? this.length - 1); i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.lastIndexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.includes = function (item, from_index) {\n\t\tconst has = includes.call(this, item, from_index);\n\n\t\tif (!has) {\n\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.includes(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn has;\n\t};\n\n\t// @ts-expect-error\n\tArray.__svelte_cleanup = () => {\n\t\tarray_prototype.indexOf = indexOf;\n\t\tarray_prototype.lastIndexOf = lastIndexOf;\n\t\tarray_prototype.includes = includes;\n\t};\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function strict_equals(a, b, equal = true) {\n\t// try-catch needed because this tries to read properties of `a` and `b`,\n\t// which could be disallowed for example in a secure context\n\ttry {\n\t\tif ((a === b) !== (get_proxied_value(a) === get_proxied_value(b))) {\n\t\t\tw.state_proxy_equality_mismatch(equal ? '===' : '!==');\n\t\t}\n\t} catch {}\n\n\treturn (a === b) === equal;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function equals(a, b, equal = true) {\n\tif ((a == b) !== (get_proxied_value(a) == get_proxied_value(b))) {\n\t\tw.state_proxy_equality_mismatch(equal ? '==' : '!=');\n\t}\n\n\treturn (a == b) === equal;\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport { DEV } from 'esm-env';\nimport { init_array_prototype_warnings } from '../dev/equality.js';\nimport { get_descriptor, is_extensible } from '../../shared/utils.js';\nimport { active_effect } from '../runtime.js';\nimport { async_mode_flag } from '../../flags/index.js';\nimport { TEXT_NODE, EFFECT_RAN } from '#client/constants';\nimport { eager_block_effects } from '../reactivity/batch.js';\n\n// export these for reference in the compiled code, making global name deduplication unnecessary\n/** @type {Window} */\nexport var $window;\n\n/** @type {Document} */\nexport var $document;\n\n/** @type {boolean} */\nexport var is_firefox;\n\n/** @type {() => Node | null} */\nvar first_child_getter;\n/** @type {() => Node | null} */\nvar next_sibling_getter;\n\n/**\n * Initialize these lazily to avoid issues when using the runtime in a server context\n * where these globals are not available while avoiding a separate server entry point\n */\nexport function init_operations() {\n\tif ($window !== undefined) {\n\t\treturn;\n\t}\n\n\t$window = window;\n\t$document = document;\n\tis_firefox = /Firefox/.test(navigator.userAgent);\n\n\tvar element_prototype = Element.prototype;\n\tvar node_prototype = Node.prototype;\n\tvar text_prototype = Text.prototype;\n\n\t// @ts-ignore\n\tfirst_child_getter = get_descriptor(node_prototype, 'firstChild').get;\n\t// @ts-ignore\n\tnext_sibling_getter = get_descriptor(node_prototype, 'nextSibling').get;\n\n\tif (is_extensible(element_prototype)) {\n\t\t// the following assignments improve perf of lookups on DOM nodes\n\t\t// @ts-expect-error\n\t\telement_prototype.__click = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__className = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__attributes = null;\n\t\t// @ts-expect-error\n\t\telement_prototype.__style = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__e = undefined;\n\t}\n\n\tif (is_extensible(text_prototype)) {\n\t\t// @ts-expect-error\n\t\ttext_prototype.__t = undefined;\n\t}\n\n\tif (DEV) {\n\t\t// @ts-expect-error\n\t\telement_prototype.__svelte_meta = null;\n\n\t\tinit_array_prototype_warnings();\n\t}\n}\n\n/**\n * @param {string} value\n * @returns {Text}\n */\nexport function create_text(value = '') {\n\treturn document.createTextNode(value);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_first_child(node) {\n\treturn first_child_getter.call(node);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_next_sibling(node) {\n\treturn next_sibling_getter.call(node);\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @template {Node} N\n * @param {N} node\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function child(node, is_text) {\n\tif (!hydrating) {\n\t\treturn get_first_child(node);\n\t}\n\n\tvar child = /** @type {TemplateNode} */ (get_first_child(hydrate_node));\n\n\t// Child can be null if we have an element with a single child, like `<p>{text}</p>`, where `text` is empty\n\tif (child === null) {\n\t\tchild = hydrate_node.appendChild(create_text());\n\t} else if (is_text && child.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\t\tchild?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(child);\n\treturn child;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {DocumentFragment | TemplateNode | TemplateNode[]} fragment\n * @param {boolean} [is_text]\n * @returns {Node | null}\n */\nexport function first_child(fragment, is_text = false) {\n\tif (!hydrating) {\n\t\t// when not hydrating, `fragment` is a `DocumentFragment` (the result of calling `open_frag`)\n\t\tvar first = /** @type {DocumentFragment} */ (get_first_child(/** @type {Node} */ (fragment)));\n\n\t\t// TODO prevent user comments with the empty string when preserveComments is true\n\t\tif (first instanceof Comment && first.data === '') return get_next_sibling(first);\n\n\t\treturn first;\n\t}\n\n\t// if an {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && hydrate_node?.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\n\t\thydrate_node?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\treturn hydrate_node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {TemplateNode} node\n * @param {number} count\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function sibling(node, count = 1, is_text = false) {\n\tlet next_sibling = hydrating ? hydrate_node : node;\n\tvar last_sibling;\n\n\twhile (count--) {\n\t\tlast_sibling = next_sibling;\n\t\tnext_sibling = /** @type {TemplateNode} */ (get_next_sibling(next_sibling));\n\t}\n\n\tif (!hydrating) {\n\t\treturn next_sibling;\n\t}\n\n\t// if a sibling {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && next_sibling?.nodeType !== TEXT_NODE) {\n\t\tvar text = create_text();\n\t\t// If the next sibling is `null` and we're handling text then it's because\n\t\t// the SSR content was empty for the text, so we need to generate a new text\n\t\t// node and insert it after the last sibling\n\t\tif (next_sibling === null) {\n\t\t\tlast_sibling?.after(text);\n\t\t} else {\n\t\t\tnext_sibling.before(text);\n\t\t}\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(next_sibling);\n\treturn /** @type {TemplateNode} */ (next_sibling);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {void}\n */\nexport function clear_text_content(node) {\n\tnode.textContent = '';\n}\n\n/**\n * Returns `true` if we're updating the current block, for example `condition` in\n * an `{#if condition}` block just changed. In this case, the branch should be\n * appended (or removed) at the same time as other updates within the\n * current `<svelte:boundary>`\n */\nexport function should_defer_append() {\n\tif (!async_mode_flag) return false;\n\tif (eager_block_effects !== null) return false;\n\n\tvar flags = /** @type {Effect} */ (active_effect).f;\n\treturn (flags & EFFECT_RAN) !== 0;\n}\n\n/**\n *\n * @param {string} tag\n * @param {string} [namespace]\n * @param {string} [is]\n * @returns\n */\nexport function create_element(tag, namespace, is) {\n\tlet options = is ? { is } : undefined;\n\tif (namespace) {\n\t\treturn document.createElementNS(namespace, tag, options);\n\t}\n\treturn document.createElement(tag, options);\n}\n\nexport function create_fragment() {\n\treturn document.createDocumentFragment();\n}\n\n/**\n * @param {string} data\n * @returns\n */\nexport function create_comment(data = '') {\n\treturn document.createComment(data);\n}\n\n/**\n * @param {Element} element\n * @param {string} key\n * @param {string} value\n * @returns\n */\nexport function set_attribute(element, key, value = '') {\n\tif (key.startsWith('xlink:')) {\n\t\telement.setAttributeNS('http://www.w3.org/1999/xlink', key, value);\n\t\treturn;\n\t}\n\treturn element.setAttribute(key, value);\n}\n", "/** @import { TemplateNode } from '#client' */\n\nimport { COMMENT_NODE } from '#client/constants';\nimport {\n\tHYDRATION_END,\n\tHYDRATION_ERROR,\n\tHYDRATION_START,\n\tHYDRATION_START_ELSE\n} from '../../../constants.js';\nimport * as w from '../warnings.js';\nimport { get_next_sibling } from './operations.js';\n\n/**\n * Use this variable to guard everything related to hydration code so it can be treeshaken out\n * if the user doesn't use the `hydrate` method and these code paths are therefore not needed.\n */\nexport let hydrating = false;\n\n/** @param {boolean} value */\nexport function set_hydrating(value) {\n\thydrating = value;\n}\n\n/**\n * The node that is currently being hydrated. This starts out as the first node inside the opening\n * <!--[--> comment, and updates each time a component calls `$.child(...)` or `$.sibling(...)`.\n * When entering a block (e.g. `{#if ...}`), `hydrate_node` is the block opening comment; by the\n * time we leave the block it is the closing comment, which serves as the block's anchor.\n * @type {TemplateNode}\n */\nexport let hydrate_node;\n\n/** @param {TemplateNode} node */\nexport function set_hydrate_node(node) {\n\tif (node === null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn (hydrate_node = node);\n}\n\nexport function hydrate_next() {\n\treturn set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(hydrate_node)));\n}\n\n/** @param {TemplateNode} node */\nexport function reset(node) {\n\tif (!hydrating) return;\n\n\t// If the node has remaining siblings, something has gone wrong\n\tif (get_next_sibling(hydrate_node) !== null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\thydrate_node = node;\n}\n\n/**\n * @param {HTMLTemplateElement} template\n */\nexport function hydrate_template(template) {\n\tif (hydrating) {\n\t\t// @ts-expect-error TemplateNode doesn't include DocumentFragment, but it's actually fine\n\t\thydrate_node = template.content;\n\t}\n}\n\nexport function next(count = 1) {\n\tif (hydrating) {\n\t\tvar i = count;\n\t\tvar node = hydrate_node;\n\n\t\twhile (i--) {\n\t\t\tnode = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\t}\n\n\t\thydrate_node = node;\n\t}\n}\n\n/**\n * Skips or removes (depending on {@link remove}) all nodes starting at `hydrate_node` up until the next hydration end comment\n * @param {boolean} remove\n */\nexport function skip_nodes(remove = true) {\n\tvar depth = 0;\n\tvar node = hydrate_node;\n\n\twhile (true) {\n\t\tif (node.nodeType === COMMENT_NODE) {\n\t\t\tvar data = /** @type {Comment} */ (node).data;\n\n\t\t\tif (data === HYDRATION_END) {\n\t\t\t\tif (depth === 0) return node;\n\t\t\t\tdepth -= 1;\n\t\t\t} else if (data === HYDRATION_START || data === HYDRATION_START_ELSE) {\n\t\t\t\tdepth += 1;\n\t\t\t}\n\t\t}\n\n\t\tvar next = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tif (remove) node.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n *\n * @param {TemplateNode} node\n */\nexport function read_hydration_instruction(node) {\n\tif (!node || node.nodeType !== COMMENT_NODE) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn /** @type {Comment} */ (node).data;\n}\n", "import { teardown } from '../../reactivity/effects.js';\nimport { define_property } from '../../../shared/utils.js';\nimport { hydrating } from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport { FILENAME } from '../../../../constants.js';\nimport * as w from '../../warnings.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { without_reactive_context } from './bindings/shared.js';\n\n/** @type {Set<string>} */\nexport const all_registered_events = new Set();\n\n/** @type {Set<(events: Array<string>) => void>} */\nexport const root_event_handles = new Set();\n\n/**\n * SSR adds onload and onerror attributes to catch those events before the hydration.\n * This function detects those cases, removes the attributes and replays the events.\n * @param {HTMLElement} dom\n */\nexport function replay_events(dom) {\n\tif (!hydrating) return;\n\n\tdom.removeAttribute('onload');\n\tdom.removeAttribute('onerror');\n\t// @ts-expect-error\n\tconst event = dom.__e;\n\tif (event !== undefined) {\n\t\t// @ts-expect-error\n\t\tdom.__e = undefined;\n\t\tqueueMicrotask(() => {\n\t\t\tif (dom.isConnected) {\n\t\t\t\tdom.dispatchEvent(event);\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * @param {string} event_name\n * @param {EventTarget} dom\n * @param {EventListener} [handler]\n * @param {AddEventListenerOptions} [options]\n */\nexport function create_event(event_name, dom, handler, options = {}) {\n\t/**\n\t * @this {EventTarget}\n\t */\n\tfunction target_handler(/** @type {Event} */ event) {\n\t\tif (!options.capture) {\n\t\t\t// Only call in the bubble phase, else delegated events would be called before the capturing events\n\t\t\thandle_event_propagation.call(dom, event);\n\t\t}\n\t\tif (!event.cancelBubble) {\n\t\t\treturn without_reactive_context(() => {\n\t\t\t\treturn handler?.call(this, event);\n\t\t\t});\n\t\t}\n\t}\n\n\t// Chrome has a bug where pointer events don't work when attached to a DOM element that has been cloned\n\t// with cloneNode() and the DOM element is disconnected from the document. To ensure the event works, we\n\t// defer the attachment till after it's been appended to the document. TODO: remove this once Chrome fixes\n\t// this bug. The same applies to wheel events and touch events.\n\tif (\n\t\tevent_name.startsWith('pointer') ||\n\t\tevent_name.startsWith('touch') ||\n\t\tevent_name === 'wheel'\n\t) {\n\t\tqueue_micro_task(() => {\n\t\t\tdom.addEventListener(event_name, target_handler, options);\n\t\t});\n\t} else {\n\t\tdom.addEventListener(event_name, target_handler, options);\n\t}\n\n\treturn target_handler;\n}\n\n/**\n * Attaches an event handler to an element and returns a function that removes the handler. Using this\n * rather than `addEventListener` will preserve the correct order relative to handlers added declaratively\n * (with attributes like `onclick`), which use event delegation for performance reasons\n *\n * @param {EventTarget} element\n * @param {string} type\n * @param {EventListener} handler\n * @param {AddEventListenerOptions} [options]\n */\nexport function on(element, type, handler, options = {}) {\n\tvar target_handler = create_event(type, element, handler, options);\n\n\treturn () => {\n\t\telement.removeEventListener(type, target_handler, options);\n\t};\n}\n\n/**\n * @param {string} event_name\n * @param {Element} dom\n * @param {EventListener} [handler]\n * @param {boolean} [capture]\n * @param {boolean} [passive]\n * @returns {void}\n */\nexport function event(event_name, dom, handler, capture, passive) {\n\tvar options = { capture, passive };\n\tvar target_handler = create_event(event_name, dom, handler, options);\n\n\tif (\n\t\tdom === document.body ||\n\t\t// @ts-ignore\n\t\tdom === window ||\n\t\t// @ts-ignore\n\t\tdom === document ||\n\t\t// Firefox has quirky behavior, it can happen that we still get \"canplay\" events when the element is already removed\n\t\tdom instanceof HTMLMediaElement\n\t) {\n\t\tteardown(() => {\n\t\t\tdom.removeEventListener(event_name, target_handler, options);\n\t\t});\n\t}\n}\n\n/**\n * @param {Array<string>} events\n * @returns {void}\n */\nexport function delegate(events) {\n\tfor (var i = 0; i < events.length; i++) {\n\t\tall_registered_events.add(events[i]);\n\t}\n\n\tfor (var fn of root_event_handles) {\n\t\tfn(events);\n\t}\n}\n\n// used to store the reference to the currently propagated event\n// to prevent garbage collection between microtasks in Firefox\n// If the event object is GCed too early, the expando __root property\n// set on the event object is lost, causing the event delegation\n// to process the event twice\nlet last_propagated_event = null;\n\n/**\n * @this {EventTarget}\n * @param {Event} event\n * @returns {void}\n */\nexport function handle_event_propagation(event) {\n\tvar handler_element = this;\n\tvar owner_document = /** @type {Node} */ (handler_element).ownerDocument;\n\tvar event_name = event.type;\n\tvar path = event.composedPath?.() || [];\n\tvar current_target = /** @type {null | Element} */ (path[0] || event.target);\n\n\tlast_propagated_event = event;\n\n\t// composedPath contains list of nodes the event has propagated through.\n\t// We check __root to skip all nodes below it in case this is a\n\t// parent of the __root node, which indicates that there's nested\n\t// mounted apps. In this case we don't want to trigger events multiple times.\n\tvar path_idx = 0;\n\n\t// the `last_propagated_event === event` check is redundant, but\n\t// without it the variable will be DCE'd and things will\n\t// fail mysteriously in Firefox\n\t// @ts-expect-error is added below\n\tvar handled_at = last_propagated_event === event && event.__root;\n\n\tif (handled_at) {\n\t\tvar at_idx = path.indexOf(handled_at);\n\t\tif (\n\t\t\tat_idx !== -1 &&\n\t\t\t(handler_element === document || handler_element === /** @type {any} */ (window))\n\t\t) {\n\t\t\t// This is the fallback document listener or a window listener, but the event was already handled\n\t\t\t// -> ignore, but set handle_at to document/window so that we're resetting the event\n\t\t\t// chain in case someone manually dispatches the same event object again.\n\t\t\t// @ts-expect-error\n\t\t\tevent.__root = handler_element;\n\t\t\treturn;\n\t\t}\n\n\t\t// We're deliberately not skipping if the index is higher, because\n\t\t// someone could create an event programmatically and emit it multiple times,\n\t\t// in which case we want to handle the whole propagation chain properly each time.\n\t\t// (this will only be a false negative if the event is dispatched multiple times and\n\t\t// the fallback document listener isn't reached in between, but that's super rare)\n\t\tvar handler_idx = path.indexOf(handler_element);\n\t\tif (handler_idx === -1) {\n\t\t\t// handle_idx can theoretically be -1 (happened in some JSDOM testing scenarios with an event listener on the window object)\n\t\t\t// so guard against that, too, and assume that everything was handled at this point.\n\t\t\treturn;\n\t\t}\n\n\t\tif (at_idx <= handler_idx) {\n\t\t\tpath_idx = at_idx;\n\t\t}\n\t}\n\n\tcurrent_target = /** @type {Element} */ (path[path_idx] || event.target);\n\t// there can only be one delegated event per element, and we either already handled the current target,\n\t// or this is the very first target in the chain which has a non-delegated listener, in which case it's safe\n\t// to handle a possible delegated event on it later (through the root delegation listener for example).\n\tif (current_target === handler_element) return;\n\n\t// Proxy currentTarget to correct target\n\tdefine_property(event, 'currentTarget', {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn current_target || owner_document;\n\t\t}\n\t});\n\n\t// This started because of Chromium issue https://chromestatus.com/feature/5128696823545856,\n\t// where removal or moving of of the DOM can cause sync `blur` events to fire, which can cause logic\n\t// to run inside the current `active_reaction`, which isn't what we want at all. However, on reflection,\n\t// it's probably best that all event handled by Svelte have this behaviour, as we don't really want\n\t// an event handler to run in the context of another reaction or effect.\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\t/**\n\t\t * @type {unknown}\n\t\t */\n\t\tvar throw_error;\n\t\t/**\n\t\t * @type {unknown[]}\n\t\t */\n\t\tvar other_errors = [];\n\n\t\twhile (current_target !== null) {\n\t\t\t/** @type {null | Element} */\n\t\t\tvar parent_element =\n\t\t\t\tcurrent_target.assignedSlot ||\n\t\t\t\tcurrent_target.parentNode ||\n\t\t\t\t/** @type {any} */ (current_target).host ||\n\t\t\t\tnull;\n\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tvar delegated = current_target['__' + event_name];\n\n\t\t\t\tif (\n\t\t\t\t\tdelegated != null &&\n\t\t\t\t\t(!(/** @type {any} */ (current_target).disabled) ||\n\t\t\t\t\t\t// DOM could've been updated already by the time this is reached, so we check this as well\n\t\t\t\t\t\t// -> the target could not have been disabled because it emits the event in the first place\n\t\t\t\t\t\tevent.target === current_target)\n\t\t\t\t) {\n\t\t\t\t\tdelegated.call(current_target, event);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (throw_error) {\n\t\t\t\t\tother_errors.push(error);\n\t\t\t\t} else {\n\t\t\t\t\tthrow_error = error;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (event.cancelBubble || parent_element === handler_element || parent_element === null) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcurrent_target = parent_element;\n\t\t}\n\n\t\tif (throw_error) {\n\t\t\tfor (let error of other_errors) {\n\t\t\t\t// Throw the rest of the errors, one-by-one on a microtask\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t\tthrow throw_error;\n\t\t}\n\t} finally {\n\t\t// @ts-expect-error is used above\n\t\tevent.__root = handler_element;\n\t\t// @ts-ignore remove proxy on currentTarget\n\t\tdelete event.currentTarget;\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * In dev, warn if an event handler is not a function, as it means the\n * user probably called the handler or forgot to add a `() =>`\n * @param {() => (event: Event, ...args: any) => void} thunk\n * @param {EventTarget} element\n * @param {[Event, ...any]} args\n * @param {any} component\n * @param {[number, number]} [loc]\n * @param {boolean} [remove_parens]\n */\nexport function apply(\n\tthunk,\n\telement,\n\targs,\n\tcomponent,\n\tloc,\n\thas_side_effects = false,\n\tremove_parens = false\n) {\n\tlet handler;\n\tlet error;\n\n\ttry {\n\t\thandler = thunk();\n\t} catch (e) {\n\t\terror = e;\n\t}\n\n\tif (typeof handler !== 'function' && (has_side_effects || handler != null || error)) {\n\t\tconst filename = component?.[FILENAME];\n\t\tconst location = loc ? ` at ${filename}:${loc[0]}:${loc[1]}` : ` in ${filename}`;\n\t\tconst phase = args[0]?.eventPhase < Event.BUBBLING_PHASE ? 'capture' : '';\n\t\tconst event_name = args[0]?.type + phase;\n\t\tconst description = `\\`${event_name}\\` handler${location}`;\n\t\tconst suggestion = remove_parens ? 'remove the trailing `()`' : 'add a leading `() =>`';\n\n\t\tw.event_handler_invalid(description, suggestion);\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\t}\n\thandler?.apply(element, args);\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAEO,IAAI,WAAW,MAAM;AACrB,IAAI,WAAW,MAAM,UAAU;AAC/B,IAAI,aAAa,MAAM;AACvB,IAAI,cAAc,OAAO;AACzB,IAAI,kBAAkB,OAAO;AAC7B,IAAI,iBAAiB,OAAO;AAC5B,IAAI,kBAAkB,OAAO;AAC7B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,kBAAkB,MAAM;AAC5B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,gBAAgB,OAAO;AAM3B,SAAS,YAAY,OAAO;AAClC,SAAO,OAAO,UAAU;AACzB;AAEO,IAAM,OAAO,MAAM;AAAC;AAUpB,SAAS,WAAW,OAAO;AACjC,SAAO,OAAO,OAAO,SAAS;AAC/B;AAGO,SAAS,IAAI,IAAI;AACvB,SAAO,GAAG;AACX;AAGO,SAAS,QAAQ,KAAK;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,QAAI,CAAC,EAAE;AAAA,EACR;AACD;AAMO,SAAS,WAAW;AAE1B,MAAI;AAGJ,MAAI;AAGJ,MAAI,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACvC,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AAGD,SAAO,EAAE,SAAS,SAAS,OAAO;AACnC;AASO,SAAS,SAAS,OAAOA,WAAU,OAAO,OAAO;AACvD,SAAO,UAAU,SACd;AAAA;AAAA,IACyBA,UAAU;AAAA;AAAA;AAAA,IAChBA;AAAA,MACnB;AACJ;AAWO,SAAS,SAAS,OAAO,GAAG;AAElC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO;AAAA,EACR;AAKA,MAAI,MAAM,UAAa,EAAE,OAAO,YAAY,QAAQ;AACnD,WAAO,MAAM,KAAK,KAAK;AAAA,EACxB;AAKA,QAAM,QAAQ,CAAC;AAEf,aAAW,WAAW,OAAO;AAC5B,UAAM,KAAK,OAAO;AAClB,QAAI,MAAM,WAAW,EAAG;AAAA,EACzB;AAEA,SAAO;AACR;;;ACpHO,IAAM,UAAU,KAAK;AACrB,IAAM,SAAS,KAAK;AACpB,IAAM,gBAAgB,KAAK;AAC3B,IAAM,eAAe,KAAK;AAC1B,IAAM,gBAAgB,KAAK;AAC3B,IAAM,cAAc,KAAK;AACzB,IAAM,kBAAkB,KAAK;AAO7B,IAAM,YAAY,KAAK;AACvB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,cAAc,KAAK;AACzB,IAAM,QAAQ,KAAK;AACnB,IAAM,YAAY,KAAK;AAIvB,IAAM,aAAa,KAAK;AAKxB,IAAM,qBAAqB,KAAK;AAChC,IAAM,eAAe,KAAK;AAC1B,IAAM,cAAc,KAAK;AACzB,IAAM,mBAAmB,KAAK;AAC9B,IAAM,cAAc,KAAK;AAQzB,IAAM,aAAa,KAAK;AAGxB,IAAM,uBAAuB,KAAK;AAClC,IAAM,QAAQ,KAAK;AAEnB,IAAM,cAAc,KAAK;AAEzB,IAAM,eAAe,OAAO,QAAQ;AACpC,IAAM,eAAe,OAAO,cAAc;AAC1C,IAAM,sBAAsB,OAAO,EAAE;AACrC,IAAM,oBAAoB,OAAO,YAAY;AAG7C,IAAM,iBAAiB,IAAK,MAAM,2BAA2B,MAAM;AAAA,EACzE,OAAO;AAAA,EACP,UAAU;AACX,EAAG;AAEI,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,eAAe;AACrB,IAAM,yBAAyB;;;ACtD/B,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAwM;AAEhO,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAAwL;AAEhN,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,4BAA4B,MAAM;AACjD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAkC,IAAI;AAAA,iDAA4G;AAE1K,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AAMO,SAAS,kBAAkB;AACjC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,qCAAkG;AAE1H,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACvD;AACD;AAMO,SAAS,6BAA6B;AAC5C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,gDAA+S;AAEvU,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA0B,IAAI;AAAA,yCAAyF;AAE/I,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,oCAAoC;AACnD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,uDAAmK;AAE3L,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,wDAAwD;AAAA,EACzE;AACD;;;AC1GO,SAAS,uBAAuB;AACtC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,0CAA2J;AAEnL,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC5D;AACD;AAMO,SAAS,8BAA8B;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,iDAAiL;AAEzM,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;AA8CO,SAAS,sBAAsB,QAAQ,WAAW;AACxD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,YAAoC,MAAM,kCAAkC,SAAS;AAAA,2CAA8E;AAE3L,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAQO,SAAS,0BAA0B,WAAW,MAAM;AAC1D,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,2BAAuD,SAAS,eAAe,IAAI;AAAA,+CAA4N;AAEvU,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAA4H;AAEpJ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AASO,SAAS,mBAAmB,GAAG,GAAG,OAAO;AAC/C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,EAAuB,QAC5C,wCAAwC,KAAK,iBAAiB,CAAC,QAAQ,CAAC,KACxE,iDAAiD,CAAC,QAAQ,CAAC,EAAE;AAAA,wCAA2C;AAE3G,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAOO,SAAS,mBAAmB,MAAM;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAyB,IAAI;AAAA,wCAA8F;AAEnJ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAA8K;AAEtM,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,cAAc,MAAM;AACnC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAoB,IAAI;AAAA,mCAAiH;AAEjK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AACD;AAMO,SAAS,kCAAkC;AACjD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,qDAA6J;AAErL,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACvE;AACD;AAMO,SAAS,+BAA+B;AAC9C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,kDAAkM;AAE1N,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,mDAAmD;AAAA,EACpE;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAA+J;AAEvL,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,uBAAuB;AACtC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,0CAA4G;AAEpI,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC5D;AACD;AAMO,SAAS,iBAAiB;AAChC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,oCAAsG;AAE9H,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACtD;AACD;AAMO,SAAS,cAAc;AAC7B,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,iCAAwH;AAEhJ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACnD;AACD;AAMO,SAAS,oCAAoC;AACnD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,uDAAgK;AAExL,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,wDAAwD;AAAA,EACzE;AACD;AAMO,SAAS,mBAAmB;AAClC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,sCAA4F;AAEpH,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACxD;AACD;AAMO,SAAS,kBAAkB;AACjC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,qCAA0M;AAElO,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACvD;AACD;AAOO,SAAS,sBAAsB,MAAM;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA4B,IAAI;AAAA,2CAAkF;AAE1I,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAOO,SAAS,oBAAoB,KAAK;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,mBAAyC,GAAG,yBAAyB,GAAG;AAAA,yCAAmE;AAEnK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,UAAU;AAC7C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,oDAA0E,QAAQ;AAAA,yCAA2D;AAErK,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAOO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,QAA8B,IAAI;AAAA,yCAAoH;AAE9K,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,yBAAyB;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,4CAAoM;AAE5N,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAmN;AAE3O,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8G;AAEtI,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAAyO;AAEjQ,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAMO,SAAS,gCAAgC;AAC/C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,mDAAsL;AAE9M,UAAM,OAAO;AAEb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACrE;AACD;;;ACzeO,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB,KAAK;AAChC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,KAAK;AAC5B,IAAM,mBAAmB,KAAK;AAC9B,IAAM,oBAAoB,KAAK;AAC/B,IAAM,wBAAwB,KAAK;AAEnC,IAAM,gBAAgB;AACtB,IAAM,iBAAiB,KAAK;AAC5B,IAAM,oBAAoB,KAAK;AAE/B,IAAM,oBAAoB;AAC1B,IAAM,2BAA2B,KAAK;AACtC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,kBAAkB;AAExB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,kBAAkB,CAAC;AAGzB,IAAM,kCAAkC,KAAK;AAC7C,IAAM,mBAAmB,KAAK;AAE9B,IAAM,gBAAgB,OAAO;AAG7B,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,MAAM,OAAO,KAAK;AAExB,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AAuBzB,IAAM,iBAAiB;;;ACrDvB,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,SAAS;AAC9C,sBAAoB;AACrB;AAGO,IAAI,YAAY;AAGhB,SAAS,cAAcC,QAAO;AACpC,cAAYA;AACb;AAYO,SAAS,gBAAgB,UAAU,MAAM,WAAW,MAAM,QAAQ,YAAY;AACpF,QAAM,SAAS;AAEf,cAAY;AAAA,IACX;AAAA,IACA,MAAM,UAAU,QAAQ;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ;AAEA,MAAI;AACH,WAAO,SAAS;AAAA,EACjB,UAAE;AACD,gBAAY;AAAA,EACb;AACD;AAYO,IAAI,iCAAiC;AAGrC,SAAS,mCAAmC,IAAI;AACtD,mCAAiC;AAClC;AAWO,SAAS,gBAAgB;AAC/B,QAAM,MAAM,CAAC;AAEb,SAAO;AAAA,IACN,MAAM;AACL,UAAI,CAAC,WAAW,GAAG,GAAG;AACrB,QAAE,gBAAgB;AAAA,MACnB;AAEA,aAAO,WAAW,GAAG;AAAA,IACtB;AAAA,IACA,CAAC,YAAY,WAAW,KAAK,OAAO;AAAA,EACrC;AACD;AAYO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,QAAM;AAAA;AAAA,IAA2B,YAAY,IAAI,GAAG;AAAA;AACpD,SAAO;AACR;AAgBO,SAAS,WAAW,KAAK,SAAS;AACxC,QAAM,cAAc,wBAAwB,YAAY;AAExD,MAAI,iBAAiB;AACpB,QAAIC;AAAA;AAAA,MAA+B,cAAe;AAAA;AAClD,QAAI,QACH,CAAC,oBACAA,SAAQ,mBAAmB;AAAA,IAE5B;AAAA,IAAmC,kBAAmB;AAEvD,QAAI,CAAC,OAAO;AACX,MAAE,uBAAuB;AAAA,IAC1B;AAAA,EACD;AAEA,cAAY,IAAI,KAAK,OAAO;AAC5B,SAAO;AACR;AASO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,SAAO,YAAY,IAAI,GAAG;AAC3B;AAUO,SAAS,iBAAiB;AAChC,QAAM,cAAc,wBAAwB,gBAAgB;AAC5D;AAAA;AAAA,IAAyB;AAAA;AAC1B;AAQO,SAAS,KAAK,OAAO,QAAQ,OAAO,IAAI;AAC9C,sBAAoB;AAAA,IACnB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG,oBAAoB,CAAC,QAAQ,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,EAAE,IAAI;AAAA,EAC/D;AAEA,MAAI,cAAK;AAER,sBAAkB,WAAW;AAC7B,qCAAiC;AAAA,EAClC;AACD;AAOO,SAAS,IAAI,WAAW;AAC9B,MAAI;AAAA;AAAA,IAA2C;AAAA;AAC/C,MAAI,UAAU,QAAQ;AAEtB,MAAI,YAAY,MAAM;AACrB,YAAQ,IAAI;AAEZ,aAAS,MAAM,SAAS;AACvB,yBAAmB,EAAE;AAAA,IACtB;AAAA,EACD;AAEA,MAAI,cAAc,QAAW;AAC5B,YAAQ,IAAI;AAAA,EACb;AAEA,UAAQ,IAAI;AAEZ,sBAAoB,QAAQ;AAE5B,MAAI,cAAK;AACR,qCAAiC,mBAAmB,YAAY;AAAA,EACjE;AAEA,SAAO;AAAA,EAA+B,CAAC;AACxC;AAGO,SAAS,WAAW;AAC1B,SAAO,CAAC,oBAAqB,sBAAsB,QAAQ,kBAAkB,MAAM;AACpF;AAMA,SAAS,wBAAwB,MAAM;AACtC,MAAI,sBAAsB,MAAM;AAC/B,IAAE,4BAA4B,IAAI;AAAA,EACnC;AAEA,SAAQ,kBAAkB,MAAM,IAAI,IAAI,mBAAmB,iBAAiB,KAAK,MAAS;AAC3F;AAMA,SAAS,mBAAmBC,oBAAmB;AAC9C,MAAI,SAASA,mBAAkB;AAC/B,SAAO,WAAW,MAAM;AACvB,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,MAAM;AACzB,aAAO;AAAA,IACR;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;;;AC7PA,IAAI,cAAc,CAAC;AAEnB,SAAS,kBAAkB;AAC1B,MAAI,QAAQ;AACZ,gBAAc,CAAC;AACf,UAAQ,KAAK;AACd;AAKO,SAAS,iBAAiB,IAAI;AACpC,MAAI,YAAY,WAAW,KAAK,CAAC,kBAAkB;AAClD,QAAI,QAAQ;AACZ,mBAAe,MAAM;AASpB,UAAI,UAAU,YAAa,iBAAgB;AAAA,IAC5C,CAAC;AAAA,EACF;AAEA,cAAY,KAAK,EAAE;AACpB;AAKO,SAAS,cAAc;AAC7B,SAAO,YAAY,SAAS,GAAG;AAC9B,oBAAgB;AAAA,EACjB;AACD;;;AChCA,IAAM,cAAc,oBAAI,QAAQ;AAKzB,SAAS,aAAa,OAAO;AACnC,MAAIC,UAAS;AAGb,MAAIA,YAAW,MAAM;AACG,IAAC,gBAAiB,KAAK;AAC9C,WAAO;AAAA,EACR;AAEA,MAAI,gBAAO,iBAAiB,SAAS,CAAC,YAAY,IAAI,KAAK,GAAG;AAC7D,gBAAY,IAAI,OAAO,gBAAgB,OAAOA,OAAM,CAAC;AAAA,EACtD;AAEA,OAAKA,QAAO,IAAI,gBAAgB,GAAG;AAGlC,SAAKA,QAAO,IAAI,qBAAqB,GAAG;AACvC,UAAI,gBAAO,CAACA,QAAO,UAAU,iBAAiB,OAAO;AACpD,0BAAkB,KAAK;AAAA,MACxB;AAEA,YAAM;AAAA,IACP;AAEwB,IAACA,QAAO,EAAG,MAAM,KAAK;AAAA,EAC/C,OAAO;AAEN,0BAAsB,OAAOA,OAAM;AAAA,EACpC;AACD;AAMO,SAAS,sBAAsB,OAAOA,SAAQ;AACpD,SAAOA,YAAW,MAAM;AACvB,SAAKA,QAAO,IAAI,qBAAqB,GAAG;AACvC,UAAI;AACqB,QAACA,QAAO,EAAG,MAAM,KAAK;AAC9C;AAAA,MACD,SAAS,GAAG;AACX,gBAAQ;AAAA,MACT;AAAA,IACD;AAEA,IAAAA,UAASA,QAAO;AAAA,EACjB;AAEA,MAAI,gBAAO,iBAAiB,OAAO;AAClC,sBAAkB,KAAK;AAAA,EACxB;AAEA,QAAM;AACP;AAOA,SAAS,gBAAgB,OAAOA,SAAQ;AACvC,QAAM,qBAAqB,eAAe,OAAO,SAAS;AAI1D,MAAI,sBAAsB,CAAC,mBAAmB,aAAc;AAE5D,MAAI,SAAS,aAAa,OAAO;AACjC,MAAI,kBAAkB;AAAA,EAAK,MAAM,MAAMA,QAAO,IAAI,QAAQ,WAAW;AACrE,MAAI,UAAUA,QAAO;AAErB,SAAO,YAAY,MAAM;AACxB,uBAAmB;AAAA,EAAK,MAAM,MAAM,QAAQ,WAAW,QAAQ,EAAE,MAAM,GAAG,EAAE,IAAI,CAAC;AACjF,cAAU,QAAQ;AAAA,EACnB;AAEA,SAAO;AAAA,IACN,SAAS,MAAM,UAAU;AAAA,EAAK,eAAe;AAAA;AAAA,IAC7C,OAAO,MAAM,OACV,MAAM,IAAI,EACX,OAAO,CAAC,SAAS,CAAC,KAAK,SAAS,qBAAqB,CAAC,EACtD,KAAK,IAAI;AAAA,EACZ;AACD;AAKA,SAAS,kBAAkB,OAAO;AACjC,QAAM,WAAW,YAAY,IAAI,KAAK;AAEtC,MAAI,UAAU;AACb,oBAAgB,OAAO,WAAW;AAAA,MACjC,OAAO,SAAS;AAAA,IACjB,CAAC;AAED,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,SAAS;AAAA,IACjB,CAAC;AAAA,EACF;AACD;;;AChHO,SAAS,OAAO,OAAO;AAC7B,SAAO,UAAU,KAAK;AACvB;AAOO,SAAS,eAAe,GAAG,GAAG;AACpC,SAAO,KAAK,IACT,KAAK,IACL,MAAM,KAAM,MAAM,QAAQ,OAAO,MAAM,YAAa,OAAO,MAAM;AACrE;AAYO,SAAS,YAAY,OAAO;AAClC,SAAO,CAAC,eAAe,OAAO,KAAK,CAAC;AACrC;;;AC1BA,IAAI,OAAO;AACX,IAAI,SAAS;AAMN,SAAS,6BAA6BC,MAAK;AACjD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,4BAAsEA,IAAG;AAAA,oDAAsG,MAAM,MAAM;AAAA,EACzM,OAAO;AACN,YAAQ,KAAK,mDAAmD;AAAA,EACjE;AACD;AAQO,SAAS,2BAA2B,YAAY;AACtD,MAAI,cAAK;AACR,YAAQ;AAAA,MACP;AAAA,IAA4C,aACzC;AAAA;AAAA,EAEJ,UAAU,KACN,iFAAiF;AAAA;AAAA,MACpF;AAAA,MACA;AAAA,IACD;AAAA,EACD,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;;;AC5BA,IAAM,QAAQ,CAAC;AASR,SAAS,SAAS,OAAO,eAAe,OAAO,YAAY,OAAO;AACxE,MAAI,gBAAO,CAAC,cAAc;AAEzB,UAAM,QAAQ,CAAC;AAEf,UAAM,OAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,OAAO,MAAM,SAAS;AAC/D,QAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAE1C,MAAE,2BAA2B;AAAA,IAC9B,WAAW,MAAM,SAAS,GAAG;AAE5B,YAAM,QAAQ,MAAM,SAAS,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE;AACvE,YAAM,SAAS,MAAM,SAAS,MAAM;AAEpC,UAAI,WAAW,MAAM,IAAI,CAAC,SAAS,YAAY,IAAI,EAAE,EAAE,KAAK,IAAI;AAChE,UAAI,SAAS,EAAG,aAAY;AAAA,WAAc,MAAM;AAEhD,MAAE,2BAA2B,QAAQ;AAAA,IACtC;AAEA,WAAO;AAAA,EACR;AAEA,SAAO,MAAM,OAAO,oBAAI,IAAI,GAAG,IAAI,OAAO,MAAM,SAAS;AAC1D;AAYA,SAAS,MAAM,OAAO,QAAQ,MAAM,OAAO,WAAW,MAAM,YAAY,OAAO;AAC9E,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,QAAI,YAAY,OAAO,IAAI,KAAK;AAChC,QAAI,cAAc,OAAW,QAAO;AAEpC,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAC1E,QAAI,iBAAiB,IAAK;AAAA;AAAA,MAAmC,IAAI,IAAI,KAAK;AAAA;AAE1E,QAAI,SAAS,KAAK,GAAG;AACpB,UAAI;AAAA;AAAA,QAAqC,MAAM,MAAM,MAAM;AAAA;AAC3D,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,YAAI,UAAU,MAAM,CAAC;AACrB,YAAI,KAAK,OAAO;AACf,eAAK,CAAC,IAAI,MAAM,SAAS,QAAQ,eAAM,GAAG,IAAI,IAAI,CAAC,MAAM,MAAM,OAAO,MAAM,SAAS;AAAA,QACtF;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,KAAK,MAAM,kBAAkB;AAEjD,aAAO,CAAC;AACR,aAAO,IAAI,OAAO,IAAI;AAEtB,UAAI,aAAa,MAAM;AACtB,eAAO,IAAI,UAAU,IAAI;AAAA,MAC1B;AAEA,eAAS,OAAO,OAAO;AACtB,aAAK,GAAG,IAAI;AAAA;AAAA,UAEX,MAAM,GAAG;AAAA,UACT;AAAA,UACA,eAAM,GAAG,IAAI,IAAI,GAAG,KAAK;AAAA,UACzB;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB,MAAM;AAC1B;AAAA;AAAA,QAAmC,gBAAgB,KAAK;AAAA;AAAA,IACzD;AAEA,QAAI;AAAA,IAA8C,MAAO,WAAY,cAAc,CAAC,WAAW;AAC9F,aAAO;AAAA;AAAA,QACiC,MAAO,OAAO;AAAA,QACrD;AAAA,QACA,eAAM,GAAG,IAAI,cAAc;AAAA,QAC3B;AAAA;AAAA,QAEA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,iBAAiB,aAAa;AAEjC;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AAEA,MAAI;AACH;AAAA;AAAA,MAAmC,gBAAgB,KAAK;AAAA;AAAA,EACzD,SAAS,GAAG;AACX,QAAI,cAAK;AACR,YAAM,KAAK,IAAI;AAAA,IAChB;AAEA;AAAA;AAAA,MAAmC;AAAA;AAAA,EACpC;AACD;;;ACzHO,IAAI,sBAAsB;AAMjC,SAAS,UAAU,QAAQ,OAAO;AACjC,QAAM,QAAQ,OAAO;AAErB,MAAI,UAAU,eAAe;AAC5B;AAAA,EACD;AAEA,QAAM,OAAO,SAAS,MAAM;AAC5B,QAAM;AAAA;AAAA,IAA4C;AAAA;AAClD,QAAM,QAAQ,OAAO,KAAK,iBAAiB,MAAM,iBAAiB,OAAO;AACzE,QAAM,QAAQ,QACX,6CACA;AAGH,UAAQ;AAAA,IACP,OAAO,QAAQ,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK,IAAI;AAAA,IACxD;AAAA,IACA,QAAQ,wBAAwB;AAAA,IAChC,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,QAC5D,SAAS,OAAO,IAAI,IACpB;AAAA,EACJ;AAEA,MAAI,SAAS,YAAY;AACxB,UAAM,OAAO,IAAI;AAAA;AAAA,MAA4B,OAAQ;AAAA,IAAI;AACzD,eAAW,OAAO,MAAM;AACvB,gBAAU,GAAG;AAAA,IACd;AAAA,EACD;AAEA,MAAI,OAAO,SAAS;AAEnB,YAAQ,IAAI,OAAO,OAAO;AAAA,EAC3B;AAEA,MAAI,SAAS,OAAO,SAAS;AAC5B,eAAW,WAAW,OAAO,QAAQ,OAAO,GAAG;AAC9C,UAAI,QAAQ,OAAO;AAElB,gBAAQ,IAAI,QAAQ,KAAK;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AAEA,MAAI,OAAO;AACV,aAASC,UAAS,MAAM,QAAQ;AAE/B,cAAQ,IAAIA,MAAK;AAAA,IAClB;AAAA,EACD;AAGA,UAAQ,SAAS;AAClB;AAMA,SAAS,SAAS,QAAQ;AACzB,OAAK,OAAO,KAAK,UAAU,YAAY,EAAG,QAAO;AACjD,SAAO,OAAO,OAAO,WAAW,GAAG,IAAI,UAAU;AAClD;AAOO,SAAS,MAAMC,QAAO,IAAI;AAChC,MAAI,iCAAiC;AAErC,MAAI;AACH,0BAAsB,EAAE,SAAS,oBAAI,IAAI,GAAG,UAAU,gBAAgB;AAEtE,QAAI,QAAQ,YAAY,IAAI;AAC5B,QAAI,QAAQ,GAAG;AACf,QAAI,QAAQ,YAAY,IAAI,IAAI,OAAO,QAAQ,CAAC;AAEhD,QAAI,SAAS,QAAQA,MAAK;AAE1B,QAAI,CAAC,gBAAgB,GAAG;AAEvB,cAAQ,IAAI,GAAG,MAAM,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC9E,WAAW,oBAAoB,QAAQ,SAAS,GAAG;AAElD,cAAQ,IAAI,GAAG,MAAM,gCAAgC,IAAI,OAAO,aAAa;AAAA,IAC9E,OAAO;AAEN,cAAQ,MAAM,GAAG,MAAM,OAAO,IAAI,OAAO,aAAa;AAEtD,UAAI,UAAU,oBAAoB;AAElC,cAAQ,MAAM;AACb,mBAAW,CAAC,QAAQ,MAAM,KAAK,SAAS;AACvC,oBAAU,QAAQ,MAAM;AAAA,QACzB;AAAA,MACD,CAAC;AAED,4BAAsB;AAGtB,cAAQ,SAAS;AAAA,IAClB;AAEA,WAAO;AAAA,EACR,UAAE;AACD,0BAAsB;AAAA,EACvB;AACD;AAMO,SAAS,UAAUA,QAAO;AAEhC,QAAM,QAAQ,MAAM;AAGpB,QAAM,kBAAkB;AACxB,MAAI,QAAQ,MAAM;AAGlB,QAAM,kBAAkB;AAExB,QAAMC,SAAQ,MAAM;AAEpB,MAAI,CAACA,OAAO,QAAO;AAEnB,QAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,QAAM,YAAY,CAAC,IAAI;AAEvB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,aAAa,KAAK,WAAW,MAAM,GAAG;AAE5C,QAAI,SAAS,SAAS;AACrB;AAAA,IACD;AAEA,QAAI,KAAK,SAAS,oBAAoB,GAAG;AACxC,aAAO;AAAA,IACR;AAEA,QAAI,WAAW,SAAS,qBAAqB,KAAK,WAAW,SAAS,oBAAoB,GAAG;AAC5F;AAAA,IACD;AAEA,cAAU,KAAK,IAAI;AAAA,EACpB;AAEA,MAAI,UAAU,WAAW,GAAG;AAC3B,WAAO;AAAA,EACR;AAEA,kBAAgB,OAAO,SAAS;AAAA,IAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,EAC3B,CAAC;AAED,kBAAgB,OAAO,QAAQ;AAAA,IAC9B,OAAOD;AAAA,EACR,CAAC;AAED;AAAA;AAAA,IAAiD;AAAA;AAClD;AAMO,SAAS,IAAIE,SAAQF,QAAO;AAClC,EAAAE,QAAO,QAAQF;AACf,YAAUE,QAAO,GAAGF,MAAK;AAEzB,SAAOE;AACR;AAMO,SAAS,UAAU,OAAOF,QAAO;AAEvC,UAAQ,iBAAiB,IAAIA,MAAK;AAClC,SAAO;AACR;AAKO,SAAS,MAAM,OAAO;AAC5B,MAAI,OAAO,UAAU,SAAU,QAAO,UAAU,MAAM,WAAW;AACjE,MAAI,OAAO,UAAU,WAAY,QAAO;AACxC,MAAI,OAAO,UAAU,YAAY,MAAO,QAAO;AAC/C,SAAO,OAAO,KAAK;AACpB;;;ACrKO,SAAS,iBAAiB,OAAO;AACvC,MAAI,cAAc;AAClB,MAAI,UAAU,OAAO,CAAC;AAEtB,MAAI;AAEJ,MAAI,cAAK;AACR,QAAI,SAAS,0BAA0B;AAAA,EACxC;AAEA,SAAO,MAAM;AACZ,QAAI,gBAAgB,GAAG;AACtB,UAAI,OAAO;AAEX,oBAAc,MAAM;AACnB,YAAI,gBAAgB,GAAG;AACtB,iBAAO,QAAQ,MAAM,MAAM,MAAM,UAAU,OAAO,CAAC,CAAC;AAAA,QACrD;AAEA,uBAAe;AAEf,eAAO,MAAM;AACZ,2BAAiB,MAAM;AAItB,2BAAe;AAEf,gBAAI,gBAAgB,GAAG;AACtB,qBAAO;AACP,qBAAO;AAIP,wBAAU,OAAO;AAAA,YAClB;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;AC5CA,IAAI,QAAQ,qBAAqB,mBAAmB;AAQ7C,SAAS,SAAS,MAAM,OAAO,UAAU;AAC/C,MAAI,SAAS,MAAM,OAAO,QAAQ;AACnC;AAEO,IAAM,WAAN,MAAe;AAAA;AAAA,EAErB;AAAA,EAEA,WAAW;AAAA;AAAA,EAGX;AAAA;AAAA,EAGA,gBAAgB,YAAY,eAAe;AAAA;AAAA,EAG3C;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA,eAAe;AAAA;AAAA,EAGf,kBAAkB;AAAA;AAAA,EAGlB,iBAAiB;AAAA;AAAA,EAGjB,sBAAsB;AAAA;AAAA,EAGtB,kBAAkB;AAAA,EAElB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EAEjB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxB,kBAAkB;AAAA,EAElB,6BAA6B,iBAAiB,MAAM;AACnD,SAAK,kBAAkB,OAAO,KAAK,oBAAoB;AAEvD,QAAI,cAAK;AACR,UAAI,KAAK,iBAAiB,mBAAmB;AAAA,IAC9C;AAEA,WAAO,MAAM;AACZ,WAAK,kBAAkB;AAAA,IACxB;AAAA,EACD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,YAAY,MAAM,OAAO,UAAU;AAClC,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,SAAK;AAAA,IAAgC,cAAe;AAEpD,SAAK,WAAW,CAAC,CAAC,KAAK,OAAO;AAE9B,SAAK,UAAU,MAAM,MAAM;AACJ,MAAC,cAAe,IAAI;AAE1C,UAAI,WAAW;AACd,cAAM,UAAU,KAAK;AACrB,qBAAa;AAEb,cAAM;AAAA;AAAA,UACmB,QAAS,aAAa;AAAA,UACtB,QAAS,SAAS;AAAA;AAE3C,YAAI,yBAAyB;AAC5B,eAAK,yBAAyB;AAAA,QAC/B,OAAO;AACN,eAAK,0BAA0B;AAAA,QAChC;AAAA,MACD,OAAO;AACN,YAAI,SAAS,KAAK,YAAY;AAE9B,YAAI;AACH,eAAK,eAAe,OAAO,MAAM,SAAS,MAAM,CAAC;AAAA,QAClD,SAAS,OAAO;AACf,eAAK,MAAM,KAAK;AAAA,QACjB;AAEA,YAAI,KAAK,iBAAiB,GAAG;AAC5B,eAAK,sBAAsB;AAAA,QAC5B,OAAO;AACN,eAAK,WAAW;AAAA,QACjB;AAAA,MACD;AAEA,aAAO,MAAM;AACZ,aAAK,iBAAiB,OAAO;AAAA,MAC9B;AAAA,IACD,GAAG,KAAK;AAER,QAAI,WAAW;AACd,WAAK,UAAU;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,4BAA4B;AAC3B,QAAI;AACH,WAAK,eAAe,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,IAC9D,SAAS,OAAO;AACf,WAAK,MAAM,KAAK;AAAA,IACjB;AAIA,SAAK,WAAW;AAAA,EACjB;AAAA,EAEA,2BAA2B;AAC1B,UAAMG,WAAU,KAAK,OAAO;AAC5B,QAAI,CAACA,UAAS;AACb;AAAA,IACD;AACA,SAAK,kBAAkB,OAAO,MAAMA,SAAQ,KAAK,OAAO,CAAC;AAEzD,UAAM,QAAQ,MAAM;AACnB,UAAI,SAAS,KAAK,YAAY;AAE9B,WAAK,eAAe,KAAK,KAAK,MAAM;AACnC,cAAM,OAAO;AACb,eAAO,OAAO,MAAM,KAAK,UAAU,MAAM,CAAC;AAAA,MAC3C,CAAC;AAED,UAAI,KAAK,iBAAiB,GAAG;AAC5B,aAAK,sBAAsB;AAAA,MAC5B,OAAO;AACN;AAAA;AAAA,UAAoC,KAAK;AAAA,UAAkB,MAAM;AAChE,iBAAK,kBAAkB;AAAA,UACxB;AAAA,QAAC;AAED,aAAK,WAAW;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,cAAc;AACb,QAAI,SAAS,KAAK;AAElB,QAAI,KAAK,UAAU;AAClB,WAAK,kBAAkB,YAAY;AACnC,WAAK,QAAQ,OAAO,KAAK,eAAe;AAExC,eAAS,KAAK;AAAA,IACf;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACZ,WAAO,KAAK,YAAa,CAAC,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW;AAAA,EAClE;AAAA,EAEA,sBAAsB;AACrB,WAAO,CAAC,CAAC,KAAK,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,IAAI;AACR,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,QAAI,eAAe;AAEnB,sBAAkB,KAAK,OAAO;AAC9B,wBAAoB,KAAK,OAAO;AAChC,0BAAsB,KAAK,QAAQ,GAAG;AAEtC,QAAI;AACH,aAAO,GAAG;AAAA,IACX,SAAS,GAAG;AACX,mBAAa,CAAC;AACd,aAAO;AAAA,IACR,UAAE;AACD,wBAAkB,eAAe;AACjC,0BAAoB,iBAAiB;AACrC,4BAAsB,YAAY;AAAA,IACnC;AAAA,EACD;AAAA,EAEA,wBAAwB;AACvB,UAAMA;AAAA;AAAA,MAAiD,KAAK,OAAO;AAAA;AAEnE,QAAI,KAAK,iBAAiB,MAAM;AAC/B,WAAK,sBAAsB,SAAS,uBAAuB;AAC3D,WAAK,oBAAoB;AAAA;AAAA,QAAoC,KAAK;AAAA,MAAgB;AAClF,kBAAY,KAAK,cAAc,KAAK,mBAAmB;AAAA,IACxD;AAEA,QAAI,KAAK,oBAAoB,MAAM;AAClC,WAAK,kBAAkB,OAAO,MAAMA,SAAQ,KAAK,OAAO,CAAC;AAAA,IAC1D;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,GAAG;AACxB,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAChC,UAAI,KAAK,QAAQ;AAChB,aAAK,OAAO,sBAAsB,CAAC;AAAA,MACpC;AAGA;AAAA,IACD;AAEA,SAAK,kBAAkB;AAEvB,QAAI,KAAK,mBAAmB,GAAG;AAC9B,WAAK,WAAW;AAEhB,UAAI,KAAK,iBAAiB;AACzB,qBAAa,KAAK,iBAAiB,MAAM;AACxC,eAAK,kBAAkB;AAAA,QACxB,CAAC;AAAA,MACF;AAEA,UAAI,KAAK,qBAAqB;AAC7B,aAAK,QAAQ,OAAO,KAAK,mBAAmB;AAC5C,aAAK,sBAAsB;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,GAAG;AACvB,SAAK,sBAAsB,CAAC;AAE5B,SAAK,wBAAwB;AAE7B,QAAI,KAAK,iBAAiB;AACzB,mBAAa,KAAK,iBAAiB,KAAK,oBAAoB;AAAA,IAC7D;AAAA,EACD;AAAA,EAEA,qBAAqB;AACpB,SAAK,2BAA2B;AAChC,WAAO;AAAA;AAAA,MAAmC,KAAK;AAAA,IAAgB;AAAA,EAChE;AAAA;AAAA,EAGA,MAAM,OAAO;AACZ,QAAI,UAAU,KAAK,OAAO;AAC1B,QAAI,SAAS,KAAK,OAAO;AAIzB,QAAI,KAAK,yBAA0B,CAAC,WAAW,CAAC,QAAS;AACxD,YAAM;AAAA,IACP;AAEA,QAAI,KAAK,cAAc;AACtB,qBAAe,KAAK,YAAY;AAChC,WAAK,eAAe;AAAA,IACrB;AAEA,QAAI,KAAK,iBAAiB;AACzB,qBAAe,KAAK,eAAe;AACnC,WAAK,kBAAkB;AAAA,IACxB;AAEA,QAAI,KAAK,gBAAgB;AACxB,qBAAe,KAAK,cAAc;AAClC,WAAK,iBAAiB;AAAA,IACvB;AAEA,QAAI,WAAW;AACd;AAAA;AAAA,QAA8C,KAAK;AAAA,MAAc;AACjE,WAAK;AACL,uBAAiB,WAAW,CAAC;AAAA,IAC9B;AAEA,QAAI,YAAY;AAChB,QAAI,mBAAmB;AAEvB,UAAMC,SAAQ,MAAM;AACnB,UAAI,WAAW;AACd,QAAE,2BAA2B;AAC7B;AAAA,MACD;AAEA,kBAAY;AAEZ,UAAI,kBAAkB;AACrB,QAAE,8BAA8B;AAAA,MACjC;AAGA,YAAM,OAAO;AAEb,WAAK,uBAAuB;AAE5B,UAAI,KAAK,mBAAmB,MAAM;AACjC,qBAAa,KAAK,gBAAgB,MAAM;AACvC,eAAK,iBAAiB;AAAA,QACvB,CAAC;AAAA,MACF;AAIA,WAAK,WAAW,KAAK,oBAAoB;AAEzC,WAAK,eAAe,KAAK,KAAK,MAAM;AACnC,aAAK,wBAAwB;AAC7B,eAAO,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,MACjD,CAAC;AAED,UAAI,KAAK,iBAAiB,GAAG;AAC5B,aAAK,sBAAsB;AAAA,MAC5B,OAAO;AACN,aAAK,WAAW;AAAA,MACjB;AAAA,IACD;AAEA,QAAI,oBAAoB;AAExB,QAAI;AACH,0BAAoB,IAAI;AACxB,yBAAmB;AACnB,gBAAU,OAAOA,MAAK;AACtB,yBAAmB;AAAA,IACpB,SAASC,QAAO;AACf,4BAAsBA,QAAO,KAAK,WAAW,KAAK,QAAQ,MAAM;AAAA,IACjE,UAAE;AACD,0BAAoB,iBAAiB;AAAA,IACtC;AAEA,QAAI,QAAQ;AACX,uBAAiB,MAAM;AACtB,aAAK,iBAAiB,KAAK,KAAK,MAAM;AACrC,gBAAM,OAAO;AACb,eAAK,wBAAwB;AAE7B,cAAI;AACH,mBAAO,OAAO,MAAM;AACnB;AAAA,gBACC,KAAK;AAAA,gBACL,MAAM;AAAA,gBACN,MAAMD;AAAA,cACP;AAAA,YACD,CAAC;AAAA,UACF,SAASC,QAAO;AACf;AAAA,cAAsBA;AAAA;AAAA,cAA8B,KAAK,QAAQ;AAAA,YAAO;AACxE,mBAAO;AAAA,UACR,UAAE;AACD,iBAAK,wBAAwB;AAAA,UAC9B;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,SAAS,eAAe;AAC9B;AAAA;AAAA;AAAA,IAAuD,cAAe;AAAA;AACvE;AAEO,SAAS,UAAU;AACzB,MAAI,kBAAkB,MAAM;AAC3B,IAAE,gCAAgC;AAAA,EACnC;AAEA,MAAIC,YAAW,cAAc;AAE7B,MAAIA,cAAa,MAAM;AACtB,WAAO;AAAA,EACR;AAEA,SAAOA,UAAS,mBAAmB;AACpC;;;AC/ZO,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,MAAM;AAC3C,sBAAoB;AACrB;AAMO,SAAS,MAAM,GAAG,GAAG;AAC3B,SAAO;AACR;AASA,SAAS,cAAcC,QAAO,YAAY,mBAAmB;AAE5D,MAAI,cAAc,CAAC;AACnB,MAAI,SAAS,WAAW;AAExB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,mBAAe,WAAW,CAAC,EAAE,GAAG,aAAa,IAAI;AAAA,EAClD;AAEA,sBAAoB,aAAa,MAAM;AAItC,QAAI,YAAY,YAAY,WAAW,KAAK,sBAAsB;AAKlE,QAAI,WAAW;AACd,UAAI;AAAA;AAAA,QAAiC;AAAA;AACrC,UAAI;AAAA;AAAA,QAAsC,OAAO;AAAA;AAEjD,yBAAmB,WAAW;AAC9B,kBAAY,OAAO,MAAM;AAEzB,MAAAA,OAAM,MAAM,MAAM;AAClB,WAAKA,QAAO,WAAW,CAAC,EAAE,MAAM,WAAW,SAAS,CAAC,EAAE,IAAI;AAAA,IAC5D;AAEA,aAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAChC,UAAI,OAAO,WAAWA,EAAC;AAEvB,UAAI,CAAC,WAAW;AACf,QAAAD,OAAM,MAAM,OAAO,KAAK,CAAC;AACzB,aAAKA,QAAO,KAAK,MAAM,KAAK,IAAI;AAAA,MACjC;AAEA,qBAAe,KAAK,GAAG,CAAC,SAAS;AAAA,IAClC;AAEA,QAAIA,OAAM,UAAU,WAAW,CAAC,GAAG;AAClC,MAAAA,OAAM,QAAQ,WAAW,CAAC,EAAE;AAAA,IAC7B;AAAA,EACD,CAAC;AACF;AAYO,SAAS,KAAK,MAAME,QAAO,gBAAgB,SAAS,WAAW,cAAc,MAAM;AACzF,MAAI,SAAS;AAGb,MAAI,QAAQ,oBAAI,IAAI;AAGpB,MAAI,QAAQ;AAEZ,MAAI,iBAAiBA,SAAQ,wBAAwB;AACrD,MAAI,qBAAqBA,SAAQ,wBAAwB;AACzD,MAAI,qBAAqBA,SAAQ,yBAAyB;AAE1D,MAAI,eAAe;AAClB,QAAI;AAAA;AAAA,MAAsC;AAAA;AAE1C,aAAS,YACN;AAAA;AAAA,MAAgD,gBAAgB,WAAW;AAAA,IAAE,IAC7E,YAAY,YAAY,YAAY,CAAC;AAAA,EACzC;AAEA,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAGA,MAAIC,YAAW;AAKf,MAAI,aAAa,mBAAmB,MAAM;AACzC,QAAI,aAAa,eAAe;AAEhC,WAAO,SAAS,UAAU,IAAI,aAAa,cAAc,OAAO,CAAC,IAAI,WAAW,UAAU;AAAA,EAC3F,CAAC;AAGD,MAAI;AAEJ,MAAI,YAAY;AAEhB,WAAS,SAAS;AACjB,cAAUH,QAAO,OAAO,QAAQE,QAAO,OAAO;AAE9C,QAAIC,cAAa,MAAM;AACtB,UAAI,MAAM,WAAW,GAAG;AACvB,YAAIA,UAAS,UAAU;AACtB,iBAAO,OAAOA,UAAS,QAAQ;AAC/B,UAAAA,UAAS,WAAW;AAAA,QACrB,OAAO;AACN,wBAAcA,UAAS,MAAM;AAAA,QAC9B;AAEA,QAAAC,QAAO,QAAQD,UAAS;AAAA,MACzB,OAAO;AACN,qBAAaA,UAAS,QAAQ,MAAM;AAInC,UAAAA,YAAW;AAAA,QACZ,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,MAAIC,UAAS,MAAM,MAAM;AACxB;AAAA,IAA4B,IAAI,UAAU;AAC1C,QAAI,SAAS,MAAM;AAGnB,QAAI,WAAW;AAEf,QAAI,WAAW;AACd,UAAI,UAAU,2BAA2B,MAAM,MAAM;AAErD,UAAI,aAAa,WAAW,IAAI;AAE/B,iBAAS,WAAW;AAEpB,yBAAiB,MAAM;AACvB,sBAAc,KAAK;AACnB,mBAAW;AAAA,MACZ;AAAA,IACD;AAEA,QAAI,OAAO,oBAAI,IAAI;AACnB,QAAI;AAAA;AAAA,MAA8B;AAAA;AAClC,QAAI,OAAO;AACX,QAAI,QAAQ,oBAAoB;AAEhC,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AACnC,UACC,aACA,aAAa,aAAa;AAAA,MACF,aAAc,SAAS,eAC9C;AAGD;AAAA,QAAiC;AACjC,mBAAW;AACX,sBAAc,KAAK;AAAA,MACpB;AAEA,UAAI,QAAQ,MAAM,CAAC;AACnB,UAAI,MAAM,QAAQ,OAAO,CAAC;AAE1B,UAAI,OAAO,YAAY,OAAO,MAAM,IAAI,GAAG;AAE3C,UAAI,MAAM;AAET,YAAI,mBAAmB;AACtB,uBAAa,KAAK,GAAG,KAAK;AAAA,QAC3B;AAEA,YAAI,mBAAmB;AACtB;AAAA;AAAA,YAA2C,KAAK;AAAA,YAAI;AAAA,UAAC;AAAA,QACtD,OAAO;AACN,eAAK,IAAI;AAAA,QACV;AAEA,YAAI,OAAO;AACV,gBAAM,gBAAgB,OAAO,KAAK,CAAC;AAAA,QACpC;AAAA,MACD,OAAO;AACN,eAAO;AAAA,UACN,YAAY,SAAS;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACAF;AAAA,UACA;AAAA,QACD;AAEA,YAAI,WAAW;AACd,eAAK,IAAI;AAET,cAAI,SAAS,MAAM;AAClB,oBAAQ;AAAA,UACT,OAAO;AACN,iBAAK,OAAO;AAAA,UACb;AAEA,iBAAO;AAAA,QACR;AAEA,cAAM,IAAI,KAAK,IAAI;AAAA,MACpB;AAEA,WAAK,IAAI,GAAG;AAAA,IACb;AAEA,QAAI,WAAW,KAAK,eAAe,CAACC,WAAU;AAC7C,UAAI,WAAW;AACd,QAAAA,YAAW;AAAA,UACV,UAAU;AAAA,UACV,QAAQ,OAAO,MAAM,YAAY,MAAM,CAAC;AAAA,QACzC;AAAA,MACD,OAAO;AACN,YAAI,WAAW,SAAS,uBAAuB;AAC/C,YAAI,SAAS,YAAY;AACzB,iBAAS,OAAO,MAAM;AAEtB,QAAAA,YAAW;AAAA,UACV;AAAA,UACA,QAAQ,OAAO,MAAM,YAAY,MAAM,CAAC;AAAA,QACzC;AAAA,MACD;AAAA,IACD;AAGA,QAAI,aAAa,SAAS,GAAG;AAC5B,uBAAiB,WAAW,CAAC;AAAA,IAC9B;AAEA,QAAI,CAAC,WAAW;AACf,UAAI,OAAO;AACV,mBAAW,CAACE,MAAKC,KAAI,KAAK,OAAO;AAChC,cAAI,CAAC,KAAK,IAAID,IAAG,GAAG;AACnB,kBAAM,gBAAgB,IAAIC,MAAK,CAAC;AAAA,UACjC;AAAA,QACD;AAEA,cAAM,SAAS,MAAM;AACrB,cAAM,UAAU,MAAM;AAAA,QAEtB,CAAC;AAAA,MACF,OAAO;AACN,eAAO;AAAA,MACR;AAAA,IACD;AAEA,QAAI,UAAU;AAEb,oBAAc,IAAI;AAAA,IACnB;AAQA,QAAI,UAAU;AAAA,EACf,CAAC;AAGD,MAAIN,SAAQ,EAAE,QAAAI,SAAQ,OAAAF,QAAO,OAAO,MAAM;AAE1C,cAAY;AAEZ,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;AAYA,SAAS,UAAUF,QAAO,OAAO,QAAQE,QAAO,SAAS;AACxD,MAAI,eAAeA,SAAQ,sBAAsB;AAEjD,MAAI,SAAS,MAAM;AACnB,MAAI,QAAQF,OAAM;AAClB,MAAI,UAAUA,OAAM;AAGpB,MAAI;AAGJ,MAAI,OAAO;AAGX,MAAI;AAGJ,MAAI,UAAU,CAAC;AAGf,MAAI,UAAU,CAAC;AAGf,MAAI;AAGJ,MAAI;AAGJ,MAAI;AAGJ,MAAI;AAEJ,MAAI,aAAa;AAChB,SAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC/B,cAAQ,MAAM,CAAC;AACf,YAAM,QAAQ,OAAO,CAAC;AACtB;AAAA,MAAgC,MAAM,IAAI,GAAG;AAE7C,WAAK,GAAG,QAAQ;AAChB,OAAC,eAAe,oBAAI,IAAI,GAAG,IAAI,IAAI;AAAA,IACpC;AAAA,EACD;AAEA,OAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC/B,YAAQ,MAAM,CAAC;AACf,UAAM,QAAQ,OAAO,CAAC;AAEtB;AAAA,IAAgC,MAAM,IAAI,GAAG;AAE7C,IAAAA,OAAM,UAAU;AAEhB,QAAI,CAAC,KAAK,GAAG;AACZ,WAAK,IAAI;AAET,UAAIO,QAAO,OAAO,KAAK,OAAO;AAE9B,WAAKP,QAAO,MAAM,IAAI;AACtB,WAAKA,QAAO,MAAMO,KAAI;AAEtB,WAAK,MAAMA,OAAM,MAAM;AACvB,aAAO;AAEP,gBAAU,CAAC;AACX,gBAAU,CAAC;AAEX,gBAAU,KAAK;AACf;AAAA,IACD;AAEA,SAAK,KAAK,EAAE,IAAI,WAAW,GAAG;AAC7B,oBAAc,KAAK,CAAC;AACpB,UAAI,aAAa;AAChB,aAAK,GAAG,MAAM;AACd,SAAC,eAAe,oBAAI,IAAI,GAAG,OAAO,IAAI;AAAA,MACvC;AAAA,IACD;AAEA,QAAI,SAAS,SAAS;AACrB,UAAI,SAAS,UAAa,KAAK,IAAI,IAAI,GAAG;AACzC,YAAI,QAAQ,SAAS,QAAQ,QAAQ;AAEpC,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI;AAEJ,iBAAO,MAAM;AAEb,cAAI,IAAI,QAAQ,CAAC;AACjB,cAAI,IAAI,QAAQ,QAAQ,SAAS,CAAC;AAElC,eAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACvC,iBAAK,QAAQ,CAAC,GAAG,OAAO,MAAM;AAAA,UAC/B;AAEA,eAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACvC,iBAAK,OAAO,QAAQ,CAAC,CAAC;AAAA,UACvB;AAEA,eAAKP,QAAO,EAAE,MAAM,EAAE,IAAI;AAC1B,eAAKA,QAAO,MAAM,CAAC;AACnB,eAAKA,QAAO,GAAG,KAAK;AAEpB,oBAAU;AACV,iBAAO;AACP,eAAK;AAEL,oBAAU,CAAC;AACX,oBAAU,CAAC;AAAA,QACZ,OAAO;AAEN,eAAK,OAAO,IAAI;AAChB,eAAK,MAAM,SAAS,MAAM;AAE1B,eAAKA,QAAO,KAAK,MAAM,KAAK,IAAI;AAChC,eAAKA,QAAO,MAAM,SAAS,OAAOA,OAAM,QAAQ,KAAK,IAAI;AACzD,eAAKA,QAAO,MAAM,IAAI;AAEtB,iBAAO;AAAA,QACR;AAEA;AAAA,MACD;AAEA,gBAAU,CAAC;AACX,gBAAU,CAAC;AAEX,aAAO,YAAY,QAAQ,QAAQ,MAAM,KAAK;AAG7C,aAAK,QAAQ,EAAE,IAAI,WAAW,GAAG;AAChC,WAAC,SAAS,oBAAI,IAAI,GAAG,IAAI,OAAO;AAAA,QACjC;AACA,gBAAQ,KAAK,OAAO;AACpB,kBAAU,QAAQ;AAAA,MACnB;AAEA,UAAI,YAAY,MAAM;AACrB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,KAAK,IAAI;AACjB,WAAO;AACP,cAAU,KAAK;AAAA,EAChB;AAEA,MAAI,YAAY,QAAQ,SAAS,QAAW;AAC3C,QAAI,aAAa,SAAS,SAAY,CAAC,IAAI,WAAW,IAAI;AAE1D,WAAO,YAAY,MAAM;AAExB,WAAK,QAAQ,EAAE,IAAI,WAAW,GAAG;AAChC,mBAAW,KAAK,OAAO;AAAA,MACxB;AACA,gBAAU,QAAQ;AAAA,IACnB;AAEA,QAAI,iBAAiB,WAAW;AAEhC,QAAI,iBAAiB,GAAG;AACvB,UAAI,qBAAqBE,SAAQ,wBAAwB,KAAK,WAAW,IAAI,SAAS;AAEtF,UAAI,aAAa;AAChB,aAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AACvC,qBAAW,CAAC,EAAE,GAAG,QAAQ;AAAA,QAC1B;AAEA,aAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AACvC,qBAAW,CAAC,EAAE,GAAG,IAAI;AAAA,QACtB;AAAA,MACD;AAEA,oBAAcF,QAAO,YAAY,iBAAiB;AAAA,IACnD;AAAA,EACD;AAEA,MAAI,aAAa;AAChB,qBAAiB,MAAM;AACtB,UAAI,eAAe,OAAW;AAC9B,WAAK,QAAQ,YAAY;AACxB,aAAK,GAAG,MAAM;AAAA,MACf;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAcA,SAAS,YAAY,QAAQ,MAAM,OAAO,KAAKQ,QAAO,WAAWN,QAAO,gBAAgB;AACvF,MAAI,qBAAqB;AACzB,MAAI,YAAYA,SAAQ,wBAAwB;AAChD,MAAI,WAAWA,SAAQ,yBAAyB;AAEhD,MAAI,IAAI,WAAY,UAAU,eAAe,OAAO,OAAO,KAAK,IAAI,OAAO,KAAK,IAAK;AACrF,MAAI,KAAKA,SAAQ,yBAAyB,IAAIM,SAAQ,OAAOA,MAAK;AAElE,MAAI,gBAAO,UAAU;AAGC,IAAC,EAAG,QAAQ,MAAM;AACtC,UAAI,mBAAmB,OAAO,MAAM,WAAWA,SAAQ,EAAE;AAEzD,qBAAe,EAAE,gBAAgB;AAAA,IAClC;AAAA,EACD;AAGA,MAAI,OAAO;AAAA,IACV;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA;AAAA,IAEH,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA,MAAM;AAAA,EACP;AAEA,sBAAoB;AAEpB,MAAI;AACH,QAAI,WAAW,MAAM;AACpB,UAAI,WAAW,SAAS,uBAAuB;AAC/C,eAAS,OAAQ,SAAS,YAAY,CAAE;AAAA,IACzC;AAEA,SAAK,IAAI,OAAO,MAAM;AAAA;AAAA,MAA+B;AAAA,MAAS;AAAA,MAAG;AAAA,MAAG;AAAA,IAAc,CAAC;AAEnF,QAAI,SAAS,MAAM;AAIlB,WAAK,OAAO;AAAA,IACb;AAEA,WAAO;AAAA,EACR,UAAE;AACD,wBAAoB;AAAA,EACrB;AACD;AAOA,SAAS,KAAK,MAAMD,OAAM,QAAQ;AACjC,MAAI,MAAM,KAAK;AAAA;AAAA,IAAoC,KAAK,KAAK,EAAE;AAAA,MAAe;AAE9E,MAAI,OAAOA;AAAA;AAAA,IAAoCA,MAAK,EAAE;AAAA,MAAe;AACrE,MAAI;AAAA;AAAA,IAAoC,KAAK,EAAE;AAAA;AAE/C,SAAO,SAAS,QAAQ,SAAS,KAAK;AACrC,QAAI;AAAA;AAAA,MAAyC,iBAAiB,IAAI;AAAA;AAClE,SAAK,OAAO,IAAI;AAChB,WAAO;AAAA,EACR;AACD;AAOA,SAAS,KAAKP,QAAO,MAAMO,OAAM;AAChC,MAAI,SAAS,MAAM;AAClB,IAAAP,OAAM,QAAQO;AACd,IAAAP,OAAM,OAAO,QAAQO,SAAQA,MAAK;AAAA,EACnC,OAAO;AACN,QAAI,KAAK,EAAE,MAAM;AAChB,WAAK,EAAE,KAAK,OAAO;AAAA,IACpB;AAEA,SAAK,OAAOA;AACZ,SAAK,EAAE,OAAOA,SAAQA,MAAK;AAAA,EAC5B;AAEA,MAAIA,UAAS,MAAM;AAClB,IAAAP,OAAM,OAAO,OAAO,QAAQ,KAAK;AAAA,EAClC,OAAO;AACN,QAAIO,MAAK,EAAE,MAAM;AAChB,MAAAA,MAAK,EAAE,KAAK,OAAO;AAAA,IACpB;AAEA,IAAAA,MAAK,OAAO;AACZ,IAAAA,MAAK,EAAE,OAAO,QAAQ,KAAK;AAAA,EAC5B;AACD;;;AC7mBO,SAAS,QAAQ,UAAU,MAAM,OAAO,IAAI;AAClD,QAAM,IAAI,SAAS,IAAI,UAAU;AAEjC,MAAI,MAAM,WAAW,KAAK,SAAS,WAAW,GAAG;AAChD,OAAG,KAAK,IAAI,CAAC,CAAC;AACd;AAAA,EACD;AAEA,MAAI,QAAQ;AACZ,MAAI;AAAA;AAAA,IAAgC;AAAA;AAEpC,MAAI,UAAU,QAAQ;AAEtB,WAASE,OAAM;AACd,YAAQ,IAAI,MAAM,IAAI,CAAC,eAAe,cAAc,UAAU,CAAC,CAAC,EAC9D,KAAK,CAAC,WAAW;AACjB,cAAQ;AAER,UAAI;AACH,WAAG,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;AAAA,MAC/B,SAAS,OAAO;AAEf,aAAK,OAAO,IAAI,eAAe,GAAG;AACjC,gCAAsB,OAAO,MAAM;AAAA,QACpC;AAAA,MACD;AAEA,aAAO,WAAW;AAClB,oBAAc;AAAA,IACf,CAAC,EACA,MAAM,CAAC,UAAU;AACjB,4BAAsB,OAAO,MAAM;AAAA,IACpC,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,SAAS,GAAG;AACxB,YAAQ,IAAI,QAAQ,EAAE,KAAK,MAAM;AAChC,cAAQ;AAER,UAAI;AACH,eAAOA,KAAI;AAAA,MACZ,UAAE;AACD,eAAO,WAAW;AAClB,sBAAc;AAAA,MACf;AAAA,IACD,CAAC;AAAA,EACF,OAAO;AACN,IAAAA,KAAI;AAAA,EACL;AACD;AAMO,SAAS,mBAAmB,UAAU,IAAI;AAChD,MAAI,YAAY;AAChB,UAAQ,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM;AAChC,0BAAsB,SAAS;AAC/B,OAAG,CAAC;AAAA,EACL,CAAC;AACF;AAOO,SAAS,UAAU;AACzB,MAAI,kBAAkB;AACtB,MAAI,oBAAoB;AACxB,MAAI,6BAA6B;AACjC,MAAIC,kBAAiB;AAErB,MAAI,cAAK;AACR,QAAI,qBAAqB;AAAA,EAC1B;AAEA,SAAO,SAAS,QAAQ,iBAAiB,MAAM;AAC9C,sBAAkB,eAAe;AACjC,wBAAoB,iBAAiB;AACrC,0BAAsB,0BAA0B;AAChD,QAAI,eAAgB,CAAAA,iBAAgB,SAAS;AAE7C,QAAI,cAAK;AACR,6BAAuB,IAAI;AAC3B,oBAAc,kBAAkB;AAAA,IACjC;AAAA,EACD;AACD;AAUA,eAAsB,KAAK,SAAS;AACnC,MAAI,UAAU,QAAQ;AACtB,MAAI,QAAQ,MAAM;AAElB,SAAO,MAAM;AACZ,YAAQ;AACR,WAAO;AAAA,EACR;AACD;AASA,eAAsB,sBAAsB,SAAS;AACpD,MAAI,wBAAwB;AAC5B,MAAI,QAAQ,MAAM;AAElB,SAAO,MAAM;AACZ,2BAAuB,qBAAqB;AAC5C,WAAO;AAAA,EACR;AACD;AAYA,gBAAuB,gCAAgC,UAAU;AAShE,QAAM,WAAW,SAAS,OAAO,aAAa,IAAI,KAAK,SAAS,OAAO,QAAQ,IAAI;AAEnF,MAAI,aAAa,QAAW;AAC3B,UAAM,IAAI,UAAU,6BAA6B;AAAA,EAClD;AAGA,MAAI,oBAAoB;AACxB,MAAI;AACH,WAAO,MAAM;AACZ,YAAM,EAAE,MAAM,MAAM,KAAK,MAAM,sBAAsB,SAAS,KAAK,CAAC,GAAG;AACvE,UAAI,MAAM;AACT,4BAAoB;AACpB;AAAA,MACD;AACA,YAAM;AAAA,IACP;AAAA,EACD,UAAE;AAED,QAAI,qBAAqB,SAAS,WAAW,QAAW;AAEvD;AAAA;AAAA,SAAgC,MAAM,sBAAsB,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA;AAAA,IACnF;AAAA,EACD;AACD;AAEO,SAAS,gBAAgB;AAC/B,oBAAkB,IAAI;AACtB,sBAAoB,IAAI;AACxB,wBAAsB,IAAI;AAE1B,MAAI,cAAK;AACR,2BAAuB,IAAI;AAC3B,kBAAc,IAAI;AAAA,EACnB;AACD;AAMA,eAAsB,WAAW,QAAQ,IAAI;AAC5C,MAAIC,YAAW,aAAa;AAC5B,MAAI;AAAA;AAAA,IAA8B;AAAA;AAClC,MAAI,WAAW,CAACA,UAAS,WAAW;AAEpC,EAAAA,UAAS,qBAAqB,CAAC;AAC/B,QAAM,UAAU,QAAQ;AAExB,MAAI;AAAA;AAAA,IAAgC;AAAA;AAEpC,MAAI,gBAAgB;AACpB,MAAI,oBAAoB;AAExB,MAAI,eAAe;AAClB,iBAAa;AACb,wBAAoB,WAAW,KAAK;AAAA,EACrC;AAEA,MAAI;AACH,QAAI,UAAU,GAAG,MAAM;AAAA,EACxB,UAAE;AACD,QAAI,mBAAmB;AACtB,uBAAiB,iBAAiB;AAClC,mBAAa;AAAA,IACd;AAAA,EACD;AAEA,MAAI;AACH,UAAM;AAAA,EACP,SAAS,OAAO;AACf,QAAI,CAAC,QAAQ,MAAM,GAAG;AACrB,4BAAsB,OAAO,MAAM;AAAA,IACpC;AAAA,EACD,UAAE;AACD,IAAAA,UAAS,qBAAqB,EAAE;AAChC,UAAM,UAAU,QAAQ;AAExB,kBAAc;AAAA,EACf;AACD;AAKO,SAASF,KAAI,QAAQ;AAC3B,QAAM,UAAU,QAAQ;AAExB,MAAIE,YAAW,aAAa;AAC5B,MAAI;AAAA;AAAA,IAA8B;AAAA;AAClC,MAAI,WAAW,CAACA,UAAS,WAAW;AAEpC,EAAAA,UAAS,qBAAqB,CAAC;AAC/B,QAAM,UAAU,QAAQ;AAExB,MAAI;AAAA;AAAA,IAAgC;AAAA;AAGpC,MAAI,UAAU;AAGd,QAAMC,gBAAe,CAAC,UAAU;AAC/B,cAAU,EAAE,MAAM;AAElB,QAAI,CAAC,QAAQ,MAAM,GAAG;AACrB,4BAAsB,OAAO,MAAM;AAAA,IACpC;AAAA,EACD;AAEA,MAAI,UAAU,QAAQ,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,MAAMA,aAAY;AAE7D,MAAI,WAAW,CAAC,OAAO;AAEvB,aAAW,MAAM,OAAO,MAAM,CAAC,GAAG;AACjC,cAAU,QACR,KAAK,MAAM;AACX,UAAI,SAAS;AACZ,cAAM,QAAQ;AAAA,MACf;AAEA,UAAI,QAAQ,MAAM,GAAG;AACpB,cAAM;AAAA,MACP;AAEA,UAAI;AACH,gBAAQ;AACR,eAAO,GAAG;AAAA,MACX,UAAE;AAED,sBAAc;AAAA,MACf;AAAA,IACD,CAAC,EACA,MAAMA,aAAY,EAClB,QAAQ,MAAM;AACd,oBAAc;AAAA,IACf,CAAC;AAEF,aAAS,KAAK,OAAO;AAAA,EACtB;AAEA,UAGE,KAAK,MAAM,QAAQ,QAAQ,CAAC,EAC5B,QAAQ,MAAM;AACd,IAAAD,UAAS,qBAAqB,EAAE;AAChC,UAAM,UAAU,QAAQ;AAAA,EACzB,CAAC;AAEF,SAAO;AACR;;;AClSO,IAAI,uBAAuB;AAG3B,SAAS,uBAAuB,GAAG;AACzC,yBAAuB;AACxB;AAEO,IAAM,wBAAwB,oBAAI,IAAI;AAQtC,SAAS,QAAQ,IAAI;AAC3B,MAAIE,SAAQ,UAAU;AACtB,MAAI,iBACH,oBAAoB,SAAS,gBAAgB,IAAI,aAAa;AAAA;AAAA,IACnC;AAAA,MACxB;AAEJ,MAAI,kBAAkB,MAAM;AAG3B,kBAAc,KAAK;AAAA,EACpB;AAGA,QAAM,SAAS;AAAA,IACd,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,GAAGA;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ;AAAA;AAAA,MAAqB;AAAA;AAAA,IACrB,IAAI;AAAA,IACJ,QAAQ,kBAAkB;AAAA,IAC1B,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAU,UAAU,YAAY;AAAA,EACxC;AAEA,SAAO;AACR;AASO,SAAS,cAAc,IAAI,UAAU;AAC3C,MAAI;AAAA;AAAA,IAAuC;AAAA;AAE3C,MAAI,WAAW,MAAM;AACpB,IAAE,qBAAqB;AAAA,EACxB;AAEA,MAAIC;AAAA;AAAA,IAAoC,OAAO;AAAA;AAE/C,MAAI;AAAA;AAAA;AAAA,IAA6D;AAAA;AACjE,MAAI,SAAS;AAAA;AAAA,IAAyB;AAAA,EAAc;AAGpD,MAAI,iBAAiB,CAAC;AAGtB,MAAI,YAAY,oBAAI,IAAI;AAExB,eAAa,MAAM;AAClB,QAAI,aAAK,wBAAuB;AAGhC,QAAI,IAAI,SAAS;AACjB,cAAU,EAAE;AAEZ,QAAI;AAIH,cAAQ,QAAQ,GAAG,CAAC,EAClB,KAAK,EAAE,SAAS,EAAE,MAAM,EACxB,KAAK,MAAM;AACX,YAAI,UAAU,iBAAiB,MAAM,WAAW;AAG/C,gBAAM,WAAW;AAAA,QAClB;AAEA,sBAAc;AAAA,MACf,CAAC;AAAA,IACH,SAAS,OAAO;AACf,QAAE,OAAO,KAAK;AACd,oBAAc;AAAA,IACf;AAEA,QAAI,aAAK,wBAAuB;AAEhC,QAAI;AAAA;AAAA,MAA8B;AAAA;AAElC,QAAI,gBAAgB;AACnB,UAAI,WAAW,CAACA,UAAS,WAAW;AAEpC,MAAAA,UAAS,qBAAqB,CAAC;AAC/B,YAAM,UAAU,QAAQ;AAExB,gBAAU,IAAI,KAAK,GAAG,OAAO,cAAc;AAC3C,gBAAU,OAAO,KAAK;AACtB,gBAAU,IAAI,OAAO,CAAC;AAAA,IACvB;AAMA,UAAM,UAAU,CAAC,OAAO,QAAQ,WAAc;AAC7C,6BAAuB;AAEvB,YAAM,SAAS;AAEf,UAAI,OAAO;AACV,YAAI,UAAU,gBAAgB;AAC7B,iBAAO,KAAK;AAGZ,uBAAa,QAAQ,KAAK;AAAA,QAC3B;AAAA,MACD,OAAO;AACN,aAAK,OAAO,IAAI,iBAAiB,GAAG;AACnC,iBAAO,KAAK;AAAA,QACb;AAEA,qBAAa,QAAQ,KAAK;AAG1B,mBAAW,CAAC,GAAGC,EAAC,KAAK,WAAW;AAC/B,oBAAU,OAAO,CAAC;AAClB,cAAI,MAAM,MAAO;AACjB,UAAAA,GAAE,OAAO,cAAc;AAAA,QACxB;AAEA,YAAI,gBAAO,aAAa,QAAW;AAClC,gCAAsB,IAAI,MAAM;AAEhC,qBAAW,MAAM;AAChB,gBAAI,sBAAsB,IAAI,MAAM,GAAG;AACtC,cAAE;AAAA;AAAA,gBAAuC,OAAO;AAAA,gBAAQ;AAAA,cAAQ;AAChE,oCAAsB,OAAO,MAAM;AAAA,YACpC;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAEA,UAAI,gBAAgB;AACnB,QAAAD,UAAS,qBAAqB,EAAE;AAChC,cAAM,UAAU,QAAQ;AAAA,MACzB;AAAA,IACD;AAEA,MAAE,QAAQ,KAAK,SAAS,CAAC,MAAM,QAAQ,MAAM,KAAK,SAAS,CAAC;AAAA,EAC7D,CAAC;AAED,WAAS,MAAM;AACd,eAAW,KAAK,UAAU,OAAO,GAAG;AACnC,QAAE,OAAO,cAAc;AAAA,IACxB;AAAA,EACD,CAAC;AAED,MAAI,cAAK;AAGR,WAAO,KAAK;AAAA,EACb;AAEA,SAAO,IAAI,QAAQ,CAAC,WAAW;AAE9B,aAASE,MAAK,GAAG;AAChB,eAAS,KAAK;AACb,YAAI,MAAM,SAAS;AAClB,iBAAO,MAAM;AAAA,QACd,OAAO;AAGN,UAAAA,MAAK,OAAO;AAAA,QACb;AAAA,MACD;AAEA,QAAE,KAAK,IAAI,EAAE;AAAA,IACd;AAEA,IAAAA,MAAK,OAAO;AAAA,EACb,CAAC;AACF;AAQO,SAAS,aAAa,IAAI;AAChC,QAAM,IAAI,QAAQ,EAAE;AAEpB,MAAI,CAAC,gBAAiB,qBAAoB,CAAC;AAE3C,SAAO;AACR;AAQO,SAAS,mBAAmB,IAAI;AACtC,QAAM,SAAS,QAAQ,EAAE;AACzB,SAAO,SAAS;AAChB,SAAO;AACR;AAMO,SAAS,wBAAwBC,UAAS;AAChD,MAAI,UAAUA,SAAQ;AAEtB,MAAI,YAAY,MAAM;AACrB,IAAAA,SAAQ,UAAU;AAElB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC3C;AAAA;AAAA,QAAsC,QAAQ,CAAC;AAAA,MAAE;AAAA,IAClD;AAAA,EACD;AACD;AAOA,IAAI,QAAQ,CAAC;AAMb,SAAS,0BAA0BA,UAAS;AAC3C,MAAI,SAASA,SAAQ;AACrB,SAAO,WAAW,MAAM;AACvB,SAAK,OAAO,IAAI,aAAa,GAAG;AAG/B,cAAQ,OAAO,IAAI,eAAe;AAAA;AAAA,QAA2B;AAAA,UAAU;AAAA,IACxE;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAOO,SAAS,gBAAgBA,UAAS;AACxC,MAAI;AACJ,MAAI,qBAAqB;AAEzB,oBAAkB,0BAA0BA,QAAO,CAAC;AAEpD,MAAI,cAAK;AACR,QAAI,qBAAqB;AACzB,sBAAkB,oBAAI,IAAI,CAAC;AAC3B,QAAI;AACH,UAAI,MAAM,SAASA,QAAO,GAAG;AAC5B,QAAE,wBAAwB;AAAA,MAC3B;AAEA,YAAM,KAAKA,QAAO;AAElB,MAAAA,SAAQ,KAAK,CAAC;AACd,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AACpC,wBAAkB,kBAAkB;AACpC,YAAM,IAAI;AAAA,IACX;AAAA,EACD,OAAO;AACN,QAAI;AACH,MAAAA,SAAQ,KAAK,CAAC;AACd,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AAAA,IACrC;AAAA,EACD;AAEA,SAAO;AACR;AAMO,SAAS,eAAeA,UAAS;AACvC,MAAI,QAAQ,gBAAgBA,QAAO;AAEnC,MAAI,CAACA,SAAQ,OAAO,KAAK,GAAG;AAK3B,QAAI,CAAC,eAAe,SAAS;AAC5B,MAAAA,SAAQ,IAAI;AAAA,IACb;AAEA,IAAAA,SAAQ,KAAK,wBAAwB;AAAA,EACtC;AAIA,MAAI,sBAAsB;AACzB;AAAA,EACD;AAIA,MAAI,iBAAiB,MAAM;AAG1B,QAAI,gBAAgB,GAAG;AACtB,mBAAa,IAAIA,UAAS,KAAK;AAAA,IAChC;AAAA,EACD,OAAO;AACN,QAAI,UAAUA,SAAQ,IAAI,eAAe,IAAI,cAAc;AAC3D,sBAAkBA,UAAS,MAAM;AAAA,EAClC;AACD;;;ACxVO,IAAI,gBAAgB,oBAAI,IAAI;AAG5B,IAAM,aAAa,oBAAI,IAAI;AAK3B,SAAS,kBAAkB,GAAG;AACpC,kBAAgB;AACjB;AAEA,IAAI,yBAAyB;AAEtB,SAAS,6BAA6B;AAC5C,2BAAyB;AAC1B;AASO,SAAS,OAAO,GAAGC,QAAO;AAEhC,MAAI,SAAS;AAAA,IACZ,GAAG;AAAA;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAUA,UAAS,UAAU,YAAY;AAChD,WAAO,UAAU;AACjB,WAAO,oBAAoB;AAC3B,WAAO,QAAQ;AAAA,EAChB;AAEA,SAAO;AACR;AAQO,SAAS,MAAM,GAAGA,QAAO;AAC/B,QAAM,IAAI,OAAO,GAAGA,MAAK;AAEzB,sBAAoB,CAAC;AAErB,SAAO;AACR;AASO,SAAS,eAAe,eAAe,YAAY,OAAO,YAAY,MAAM;AAClF,QAAM,IAAI,OAAO,aAAa;AAC9B,MAAI,CAAC,WAAW;AACf,MAAE,SAAS;AAAA,EACZ;AAIA,MAAI,oBAAoB,aAAa,sBAAsB,QAAQ,kBAAkB,MAAM,MAAM;AAChG,KAAC,kBAAkB,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO;AACR;AAOO,SAAS,OAAOC,SAAQ,OAAO;AACrC;AAAA,IACCA;AAAA,IACA,QAAQ,MAAM,IAAIA,OAAM,CAAC;AAAA,EAC1B;AACA,SAAO;AACR;AASO,SAAS,IAAIA,SAAQ,OAAO,eAAe,OAAO;AACxD,MACC,oBAAoB;AAAA;AAAA,GAGnB,CAAC,eAAe,gBAAgB,IAAI,kBAAkB,MACvD,SAAS,MACR,gBAAgB,KAAK,UAAU,eAAe,QAAQ,mBAAmB,KAC1E,CAAC,iBAAiB,SAASA,OAAM,GAChC;AACD,IAAE,sBAAsB;AAAA,EACzB;AAEA,MAAI,YAAY,eAAe,MAAM,KAAK,IAAI;AAE9C,MAAI,cAAK;AACR;AAAA,MAAU;AAAA;AAAA,MAAkCA,QAAO;AAAA,IAAM;AAAA,EAC1D;AAEA,SAAO,aAAaA,SAAQ,SAAS;AACtC;AAQO,SAAS,aAAaA,SAAQ,OAAO;AAC3C,MAAI,CAACA,QAAO,OAAO,KAAK,GAAG;AAC1B,QAAI,YAAYA,QAAO;AAEvB,QAAI,sBAAsB;AACzB,iBAAW,IAAIA,SAAQ,KAAK;AAAA,IAC7B,OAAO;AACN,iBAAW,IAAIA,SAAQ,SAAS;AAAA,IACjC;AAEA,IAAAA,QAAO,IAAI;AAEX,QAAI,QAAQ,MAAM,OAAO;AACzB,UAAM,QAAQA,SAAQ,SAAS;AAE/B,QAAI,cAAK;AACR,UAAI,qBAAqB,kBAAkB,MAAM;AAChD,QAAAA,QAAO,YAAY,oBAAI,IAAI;AAI3B,cAAM,SAASA,QAAO,QAAQ,IAAI,EAAE,GAAG,SAAS,KAAK;AACrD,QAAAA,QAAO,QAAQ,IAAI,IAAI,EAAE;AAAA;AAAA,UAA2B;AAAA,WAAO,MAAM,CAAC;AAElE,YAAI,qBAAqB,QAAQ,GAAG;AACnC,gBAAM,QAAQ,UAAU,YAAY;AAEpC,cAAI,UAAU,MAAM;AACnB,gBAAI,QAAQA,QAAO,QAAQ,IAAI,MAAM,KAAK;AAE1C,gBAAI,CAAC,OAAO;AACX,sBAAQ,EAAE,OAAO,OAAO,EAAE;AAC1B,cAAAA,QAAO,QAAQ,IAAI,MAAM,OAAO,KAAK;AAAA,YACtC;AAEA,kBAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD;AAEA,UAAI,kBAAkB,MAAM;AAC3B,QAAAA,QAAO,oBAAoB;AAAA,MAC5B;AAAA,IACD;AAEA,SAAKA,QAAO,IAAI,aAAa,GAAG;AAE/B,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B;AAAA;AAAA,UAAwCA;AAAA,QAAO;AAAA,MAChD;AAEA,wBAAkBA,UAASA,QAAO,IAAI,eAAe,IAAI,QAAQ,WAAW;AAAA,IAC7E;AAEA,IAAAA,QAAO,KAAK,wBAAwB;AAEpC,mBAAeA,SAAQ,KAAK;AAM5B,QACC,SAAS,KACT,kBAAkB,SACjB,cAAc,IAAI,WAAW,MAC7B,cAAc,KAAK,gBAAgB,kBAAkB,GACrD;AACD,UAAI,qBAAqB,MAAM;AAC9B,6BAAqB,CAACA,OAAM,CAAC;AAAA,MAC9B,OAAO;AACN,yBAAiB,KAAKA,OAAM;AAAA,MAC7B;AAAA,IACD;AAEA,QAAI,CAAC,MAAM,WAAW,cAAc,OAAO,KAAK,CAAC,wBAAwB;AACxE,0BAAoB;AAAA,IACrB;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,sBAAsB;AACrC,2BAAyB;AACzB,MAAI,0BAA0B;AAC9B,yBAAuB,IAAI;AAE3B,QAAM,WAAW,MAAM,KAAK,aAAa;AAEzC,MAAI;AACH,eAAWC,WAAU,UAAU;AAG9B,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,0BAAkBA,SAAQ,WAAW;AAAA,MACtC;AAEA,UAAI,SAASA,OAAM,GAAG;AACrB,sBAAcA,OAAM;AAAA,MACrB;AAAA,IACD;AAAA,EACD,UAAE;AACD,2BAAuB,uBAAuB;AAAA,EAC/C;AAEA,gBAAc,MAAM;AACrB;AAQO,SAAS,OAAOD,SAAQ,IAAI,GAAG;AACrC,MAAI,QAAQ,IAAIA,OAAM;AACtB,MAAI,SAAS,MAAM,IAAI,UAAU;AAEjC,MAAIA,SAAQ,KAAK;AAGjB,SAAO;AACR;AAQO,SAAS,WAAWA,SAAQ,IAAI,GAAG;AACzC,MAAI,QAAQ,IAAIA,OAAM;AAGtB,SAAO,IAAIA,SAAQ,MAAM,IAAI,EAAE,QAAQ,EAAE,KAAK;AAC/C;AAMO,SAAS,UAAUA,SAAQ;AACjC,MAAIA,SAAQA,QAAO,IAAI,CAAC;AACzB;AAOA,SAAS,eAAe,QAAQ,QAAQ;AACvC,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS,UAAU;AAEvB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAIE,SAAQ,SAAS;AAGrB,QAAI,CAAC,SAAS,aAAa,cAAe;AAG1C,QAAI,iBAAQA,SAAQ,kBAAkB,GAAG;AACxC,oBAAc,IAAI,QAAQ;AAC1B;AAAA,IACD;AAEA,QAAI,aAAaA,SAAQ,WAAW;AAGpC,QAAI,WAAW;AACd,wBAAkB,UAAU,MAAM;AAAA,IACnC;AAEA,SAAKA,SAAQ,aAAa,GAAG;AAC5B,UAAIC;AAAA;AAAA,QAAkC;AAAA;AAEtC,oBAAc,OAAOA,QAAO;AAE5B,WAAKD,SAAQ,gBAAgB,GAAG;AAE/B,YAAIA,SAAQ,WAAW;AACtB,mBAAS,KAAK;AAAA,QACf;AAEA,uBAAeC,UAAS,WAAW;AAAA,MACpC;AAAA,IACD,WAAW,WAAW;AACrB,WAAKD,SAAQ,kBAAkB,GAAG;AACjC,YAAI,wBAAwB,MAAM;AACjC,8BAAoB;AAAA;AAAA,YAA2B;AAAA,UAAS;AAAA,QACzD;AAAA,MACD;AAEA;AAAA;AAAA,QAAuC;AAAA,MAAS;AAAA,IACjD;AAAA,EACD;AACD;;;ACpUA,IAAM,UAAU,oBAAI,IAAI;AAGjB,IAAI,gBAAgB;AAOpB,IAAI,iBAAiB;AAQrB,IAAI,eAAe;AAI1B,IAAI,sBAAsB,CAAC;AAG3B,IAAI,wBAAwB;AAE5B,IAAI,cAAc;AACX,IAAI,mBAAmB;AAEvB,IAAM,QAAN,MAAM,OAAM;AAAA,EAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,UAAU,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,WAAW,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,oBAAoB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,qBAAqB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW;AAAA;AAAA;AAAA;AAAA,EAKX,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,uBAAuB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,kBAAkB,oBAAI,IAAI;AAAA,EAE1B,UAAU;AAAA,EAEV,cAAc;AACb,WAAO,KAAK,WAAW,KAAK,oBAAoB;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,cAAc;AACrB,0BAAsB,CAAC;AAEvB,qBAAiB;AAEjB,SAAK,MAAM;AAGX,QAAI,SAAS;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS,CAAC;AAAA,MACV,gBAAgB,CAAC;AAAA,MACjB,eAAe,CAAC;AAAA,IACjB;AAEA,eAAW,QAAQ,cAAc;AAChC,WAAK,sBAAsB,MAAM,MAAM;AAAA,IAGxC;AAEA,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,SAAS;AAAA,IACf;AAEA,QAAI,KAAK,YAAY,GAAG;AACvB,WAAK,eAAe,OAAO,OAAO;AAClC,WAAK,eAAe,OAAO,cAAc;AACzC,WAAK,eAAe,OAAO,aAAa;AAAA,IACzC,OAAO;AAGN,uBAAiB;AACjB,sBAAgB;AAEhB,2BAAqB,OAAO,cAAc;AAC1C,2BAAqB,OAAO,OAAO;AAEnC,uBAAiB;AAEjB,WAAK,WAAW,QAAQ;AAAA,IACzB;AAEA,mBAAe;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,MAAM,QAAQ;AACnC,SAAK,KAAK;AAEV,QAAIE,UAAS,KAAK;AAElB,WAAOA,YAAW,MAAM;AACvB,UAAIC,SAAQD,QAAO;AACnB,UAAI,aAAaC,UAAS,gBAAgB,kBAAkB;AAC5D,UAAI,sBAAsB,cAAcA,SAAQ,WAAW;AAE3D,UAAI,OAAO,wBAAwBA,SAAQ,WAAW,KAAK,KAAK,gBAAgB,IAAID,OAAM;AAE1F,WAAKA,QAAO,IAAI,qBAAqB,KAAKA,QAAO,GAAG,WAAW,GAAG;AACjE,iBAAS;AAAA,UACR,QAAQ;AAAA,UACR,QAAAA;AAAA,UACA,SAAS,CAAC;AAAA,UACV,gBAAgB,CAAC;AAAA,UACjB,eAAe,CAAC;AAAA,QACjB;AAAA,MACD;AAEA,UAAI,CAAC,QAAQA,QAAO,OAAO,MAAM;AAChC,YAAI,WAAW;AACd,UAAAA,QAAO,KAAK;AAAA,QACb,YAAYC,SAAQ,YAAY,GAAG;AAClC,iBAAO,QAAQ,KAAKD,OAAM;AAAA,QAC3B,WAAW,oBAAoBC,SAAQ,mBAAmB,GAAG;AAC5D,iBAAO,eAAe,KAAKD,OAAM;AAAA,QAClC,WAAW,SAASA,OAAM,GAAG;AAC5B,eAAKA,QAAO,IAAI,kBAAkB,EAAG,QAAO,cAAc,KAAKA,OAAM;AACrE,wBAAcA,OAAM;AAAA,QACrB;AAEA,YAAIE,SAAQF,QAAO;AAEnB,YAAIE,WAAU,MAAM;AACnB,UAAAF,UAASE;AACT;AAAA,QACD;AAAA,MACD;AAEA,UAAI,SAASF,QAAO;AACpB,MAAAA,UAASA,QAAO;AAEhB,aAAOA,YAAW,QAAQ,WAAW,MAAM;AAC1C,YAAI,WAAW,OAAO,QAAQ;AAI7B,eAAK,eAAe,OAAO,OAAO;AAClC,eAAK,eAAe,OAAO,cAAc;AACzC,eAAK,eAAe,OAAO,aAAa;AAExC;AAAA,UAAsC,OAAO;AAAA,QAC9C;AAEA,QAAAA,UAAS,OAAO;AAChB,iBAAS,OAAO;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACvB,eAAW,KAAK,SAAS;AACxB,YAAM,UAAU,EAAE,IAAI,WAAW,IAAI,KAAK,iBAAiB,KAAK;AAChE,aAAO,KAAK,CAAC;AAIb,WAAK,cAAc,EAAE,IAAI;AAGzB,wBAAkB,GAAG,KAAK;AAAA,IAC3B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,MAAM;AACnB,QAAI,SAAS,KAAM;AAEnB,eAAW,OAAO,MAAM;AACvB,WAAK,IAAI,IAAI,aAAa,MAAM,IAAI,IAAI,gBAAgB,GAAG;AAC1D;AAAA,MACD;AAEA,UAAI,KAAK;AAET,WAAK;AAAA;AAAA,QAAsC,IAAK;AAAA,MAAI;AAAA,IACrD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQG,SAAQ,OAAO;AACtB,QAAI,CAAC,KAAK,SAAS,IAAIA,OAAM,GAAG;AAC/B,WAAK,SAAS,IAAIA,SAAQ,KAAK;AAAA,IAChC;AAGA,SAAKA,QAAO,IAAI,iBAAiB,GAAG;AACnC,WAAK,QAAQ,IAAIA,SAAQA,QAAO,CAAC;AACjC,oBAAc,IAAIA,SAAQA,QAAO,CAAC;AAAA,IACnC;AAAA,EACD;AAAA,EAEA,WAAW;AACV,oBAAgB;AAChB,SAAK,MAAM;AAAA,EACZ;AAAA,EAEA,aAAa;AAGZ,QAAI,kBAAkB,KAAM;AAE5B,oBAAgB;AAChB,mBAAe;AAAA,EAChB;AAAA,EAEA,QAAQ;AACP,SAAK,SAAS;AAEd,QAAI,oBAAoB,SAAS,GAAG;AACnC,oBAAc;AAEd,UAAI,kBAAkB,QAAQ,kBAAkB,MAAM;AAErD;AAAA,MACD;AAAA,IACD,WAAW,KAAK,aAAa,GAAG;AAC/B,WAAK,QAAQ,CAAC,CAAC;AAAA,IAChB;AAEA,SAAK,WAAW;AAAA,EACjB;AAAA,EAEA,UAAU;AACT,eAAW,MAAM,KAAK,mBAAoB,IAAG,IAAI;AACjD,SAAK,mBAAmB,MAAM;AAAA,EAC/B;AAAA,EAEA,WAAW;AACV,QAAI,KAAK,sBAAsB,GAAG;AAEjC,iBAAW,MAAM,KAAK,kBAAmB,IAAG;AAC5C,WAAK,kBAAkB,MAAM;AAAA,IAC9B;AAEA,QAAI,KAAK,aAAa,GAAG;AACxB,WAAK,QAAQ;AAAA,IACd;AAAA,EACD;AAAA,EAEA,UAAU;AAKT,QAAI,QAAQ,OAAO,GAAG;AACrB,WAAK,SAAS,MAAM;AAEpB,UAAI,wBAAwB;AAC5B,UAAI,aAAa;AAGjB,UAAI,eAAe;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS,CAAC;AAAA,QACV,gBAAgB,CAAC;AAAA,QACjB,eAAe,CAAC;AAAA,MACjB;AAEA,iBAAW,SAAS,SAAS;AAC5B,YAAI,UAAU,MAAM;AACnB,uBAAa;AACb;AAAA,QACD;AAGA,cAAM,UAAU,CAAC;AAEjB,mBAAW,CAACA,SAAQ,KAAK,KAAK,KAAK,SAAS;AAC3C,cAAI,MAAM,QAAQ,IAAIA,OAAM,GAAG;AAC9B,gBAAI,cAAc,UAAU,MAAM,QAAQ,IAAIA,OAAM,GAAG;AAEtD,oBAAM,QAAQ,IAAIA,SAAQ,KAAK;AAAA,YAChC,OAAO;AAGN;AAAA,YACD;AAAA,UACD;AAEA,kBAAQ,KAAKA,OAAM;AAAA,QACpB;AAEA,YAAI,QAAQ,WAAW,GAAG;AACzB;AAAA,QACD;AAGA,cAAM,SAAS,CAAC,GAAG,MAAM,QAAQ,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC;AAC3E,YAAI,OAAO,SAAS,GAAG;AAEtB,cAAI,2BAA2B;AAC/B,gCAAsB,CAAC;AAGvB,gBAAM,SAAS,oBAAI,IAAI;AAEvB,gBAAM,UAAU,oBAAI,IAAI;AACxB,qBAAWA,WAAU,SAAS;AAC7B,yBAAaA,SAAQ,QAAQ,QAAQ,OAAO;AAAA,UAC7C;AAEA,cAAI,oBAAoB,SAAS,GAAG;AACnC,4BAAgB;AAChB,kBAAM,MAAM;AAEZ,uBAAW,QAAQ,qBAAqB;AACvC,oBAAM,sBAAsB,MAAM,YAAY;AAAA,YAC/C;AAIA,kBAAM,WAAW;AAAA,UAClB;AAEA,gCAAsB;AAAA,QACvB;AAAA,MACD;AAEA,sBAAgB;AAChB,qBAAe;AAAA,IAChB;AAEA,SAAK,YAAY;AACjB,YAAQ,OAAO,IAAI;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,UAAU;AACnB,SAAK,YAAY;AACjB,QAAI,SAAU,MAAK,qBAAqB;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,UAAU;AACnB,SAAK,YAAY;AACjB,QAAI,SAAU,MAAK,qBAAqB;AAExC,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,SAAS;AACR,eAAW,KAAK,KAAK,gBAAgB;AACpC,wBAAkB,GAAG,KAAK;AAC1B,sBAAgB,CAAC;AAAA,IAClB;AAEA,eAAW,KAAK,KAAK,sBAAsB;AAC1C,wBAAkB,GAAG,WAAW;AAChC,sBAAgB,CAAC;AAAA,IAClB;AAEA,SAAK,iBAAiB,CAAC;AACvB,SAAK,uBAAuB,CAAC;AAE7B,SAAK,MAAM;AAAA,EACZ;AAAA;AAAA,EAGA,SAAS,IAAI;AACZ,SAAK,kBAAkB,IAAI,EAAE;AAAA,EAC9B;AAAA;AAAA,EAGA,UAAU,IAAI;AACb,SAAK,mBAAmB,IAAI,EAAE;AAAA,EAC/B;AAAA,EAEA,UAAU;AACT,YAAQ,KAAK,cAAc,SAAS,GAAG;AAAA,EACxC;AAAA,EAEA,OAAO,SAAS;AACf,QAAI,kBAAkB,MAAM;AAC3B,YAAM,QAAS,gBAAgB,IAAI,OAAM;AACzC,cAAQ,IAAI,aAAa;AAEzB,UAAI,CAAC,kBAAkB;AACtB,eAAM,QAAQ,MAAM;AACnB,cAAI,kBAAkB,OAAO;AAE5B;AAAA,UACD;AAEA,gBAAM,MAAM;AAAA,QACb,CAAC;AAAA,MACF;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,OAAO,QAAQ,MAAM;AACpB,qBAAiB,IAAI;AAAA,EACtB;AAAA,EAEA,QAAQ;AACP,QAAI,CAAC,mBAAoB,CAAC,KAAK,WAAW,QAAQ,SAAS,EAAI;AAI/D,mBAAe,IAAI,IAAI,KAAK,OAAO;AAGnC,eAAW,SAAS,SAAS;AAC5B,UAAI,UAAU,KAAM;AAEpB,iBAAW,CAACA,SAAQ,QAAQ,KAAK,MAAM,UAAU;AAChD,YAAI,CAAC,aAAa,IAAIA,OAAM,GAAG;AAC9B,uBAAa,IAAIA,SAAQ,QAAQ;AAAA,QAClC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AASO,SAAS,UAAU,IAAI;AAC7B,MAAI,mBAAmB,kBAAkB,MAAM;AAE9C,IAAE,qBAAqB;AAAA,EACxB;AAEA,MAAI,oBAAoB;AACxB,qBAAmB;AAEnB,MAAI;AACH,QAAI;AAEJ,QAAI,IAAI;AACP,UAAI,kBAAkB,MAAM;AAC3B,sBAAc;AAAA,MACf;AAEA,eAAS,GAAG;AAAA,IACb;AAEA,WAAO,MAAM;AACZ,kBAAY;AAEZ,UAAI,oBAAoB,WAAW,GAAG;AACrC,uBAAe,MAAM;AAGrB,YAAI,oBAAoB,WAAW,GAAG;AAGrC,kCAAwB;AAExB;AAAA;AAAA,YAAyB;AAAA;AAAA,QAC1B;AAAA,MACD;AAEA,oBAAc;AAAA,IACf;AAAA,EACD,UAAE;AACD,uBAAmB;AAAA,EACpB;AACD;AAEA,SAAS,gBAAgB;AACxB,MAAI,sBAAsB;AAC1B,gBAAc;AAEd,MAAI,gBAAgB,eAAM,oBAAI,IAAI,IAAI;AAEtC,MAAI;AACH,QAAI,cAAc;AAClB,2BAAuB,IAAI;AAE3B,WAAO,oBAAoB,SAAS,GAAG;AACtC,UAAI,QAAQ,MAAM,OAAO;AAEzB,UAAI,gBAAgB,KAAM;AACzB,YAAI,cAAK;AACR,cAAI,UAAU,oBAAI,IAAI;AAEtB,qBAAWA,WAAU,MAAM,QAAQ,KAAK,GAAG;AAC1C,uBAAW,CAACC,QAAOC,OAAM,KAAKF,QAAO,WAAW,CAAC,GAAG;AACnD,kBAAI,QAAQ,QAAQ,IAAIC,MAAK;AAE7B,kBAAI,CAAC,OAAO;AACX,wBAAQ,EAAE,OAAOC,QAAO,OAAO,OAAO,EAAE;AACxC,wBAAQ,IAAID,QAAO,KAAK;AAAA,cACzB;AAEA,oBAAM,SAASC,QAAO;AAAA,YACvB;AAAA,UACD;AAEA,qBAAWA,WAAU,QAAQ,OAAO,GAAG;AACtC,gBAAIA,QAAO,OAAO;AAEjB,sBAAQ,MAAMA,QAAO,KAAK;AAAA,YAC3B;AAAA,UACD;AAAA,QACD;AAEA,4BAAoB;AAAA,MACrB;AAEA,YAAM,QAAQ,mBAAmB;AACjC,iBAAW,MAAM;AAEjB,UAAI,cAAK;AACR,mBAAWF,WAAU,MAAM,QAAQ,KAAK,GAAG;AACf,UAAC,cAAe,IAAIA,OAAM;AAAA,QACtD;AAAA,MACD;AAAA,IACD;AAAA,EACD,UAAE;AACD,kBAAc;AACd,2BAAuB,mBAAmB;AAE1C,4BAAwB;AAExB,QAAI,cAAK;AACR;AAAA,cAAWA;AAAA;AAAA,QAAsC;AAAA,QAAgB;AAChE,QAAAA,QAAO,UAAU;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AACD;AAEA,SAAS,sBAAsB;AAC9B,MAAI;AACH,IAAE,6BAA6B;AAAA,EAChC,SAAS,OAAO;AACf,QAAI,cAAK;AAER,sBAAgB,OAAO,SAAS,EAAE,OAAO,GAAG,CAAC;AAAA,IAC9C;AAIA,0BAAsB,OAAO,qBAAqB;AAAA,EACnD;AACD;AAGO,IAAI,sBAAsB;AAMjC,SAAS,qBAAqB,SAAS;AACtC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,EAAG;AAElB,MAAI,IAAI;AAER,SAAO,IAAI,QAAQ;AAClB,QAAIH,UAAS,QAAQ,GAAG;AAExB,SAAKA,QAAO,KAAK,YAAY,YAAY,KAAK,SAASA,OAAM,GAAG;AAC/D,4BAAsB,oBAAI,IAAI;AAE9B,oBAAcA,OAAM;AAOpB,UAAIA,QAAO,SAAS,QAAQA,QAAO,UAAU,QAAQA,QAAO,gBAAgB,MAAM;AAGjF,YAAIA,QAAO,aAAa,QAAQA,QAAO,OAAO,MAAM;AAEnD,wBAAcA,OAAM;AAAA,QACrB,OAAO;AAEN,UAAAA,QAAO,KAAK;AAAA,QACb;AAAA,MACD;AAIA,UAAI,qBAAqB,OAAO,GAAG;AAClC,mBAAW,MAAM;AAEjB,mBAAW,KAAK,qBAAqB;AAEpC,eAAK,EAAE,KAAK,YAAY,YAAY,EAAG;AAIvC,gBAAM,kBAAkB,CAAC,CAAC;AAC1B,cAAI,WAAW,EAAE;AACjB,iBAAO,aAAa,MAAM;AACzB,gBAAI,oBAAoB,IAAI,QAAQ,GAAG;AACtC,kCAAoB,OAAO,QAAQ;AACnC,8BAAgB,KAAK,QAAQ;AAAA,YAC9B;AACA,uBAAW,SAAS;AAAA,UACrB;AAEA,mBAAS,IAAI,gBAAgB,SAAS,GAAG,KAAK,GAAG,KAAK;AACrD,kBAAMM,KAAI,gBAAgB,CAAC;AAE3B,iBAAKA,GAAE,KAAK,YAAY,YAAY,EAAG;AACvC,0BAAcA,EAAC;AAAA,UAChB;AAAA,QACD;AAEA,4BAAoB,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,EACD;AAEA,wBAAsB;AACvB;AAWA,SAAS,aAAa,OAAO,SAAS,QAAQ,SAAS;AACtD,MAAI,OAAO,IAAI,KAAK,EAAG;AACvB,SAAO,IAAI,KAAK;AAEhB,MAAI,MAAM,cAAc,MAAM;AAC7B,eAAW,YAAY,MAAM,WAAW;AACvC,YAAML,SAAQ,SAAS;AAEvB,WAAKA,SAAQ,aAAa,GAAG;AAC5B;AAAA;AAAA,UAAqC;AAAA,UAAW;AAAA,UAAS;AAAA,UAAQ;AAAA,QAAO;AAAA,MACzE,YACEA,UAAS,QAAQ,mBAAmB,MACpCA,SAAQ,WAAW;AAAA,MACpB,WAAW,UAAU,SAAS,OAAO,GACpC;AACD,0BAAkB,UAAU,KAAK;AACjC;AAAA;AAAA,UAAuC;AAAA,QAAS;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AACD;AASA,SAAS,mBAAmB,OAAO,SAAS;AAC3C,MAAI,MAAM,cAAc,KAAM;AAE9B,aAAW,YAAY,MAAM,WAAW;AACvC,UAAMA,SAAQ,SAAS;AAEvB,SAAKA,SAAQ,aAAa,GAAG;AAC5B;AAAA;AAAA,QAA2C;AAAA,QAAW;AAAA,MAAO;AAAA,IAC9D,YAAYA,SAAQ,kBAAkB,GAAG;AACxC,wBAAkB,UAAU,KAAK;AACjC,cAAQ;AAAA;AAAA,QAA2B;AAAA,MAAS;AAAA,IAC7C;AAAA,EACD;AACD;AAOA,SAAS,WAAW,UAAU,SAAS,SAAS;AAC/C,QAAM,UAAU,QAAQ,IAAI,QAAQ;AACpC,MAAI,YAAY,OAAW,QAAO;AAElC,MAAI,SAAS,SAAS,MAAM;AAC3B,eAAW,OAAO,SAAS,MAAM;AAChC,UAAI,QAAQ,SAAS,GAAG,GAAG;AAC1B,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,IAAI,aAAa,KAAK;AAAA;AAAA,QAAmC;AAAA,QAAM;AAAA,QAAS;AAAA,MAAO,GAAG;AAC1F,gBAAQ;AAAA;AAAA,UAA4B;AAAA,UAAM;AAAA,QAAI;AAC9C,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAEA,UAAQ,IAAI,UAAU,KAAK;AAE3B,SAAO;AACR;AAMO,SAAS,gBAAgB,QAAQ;AACvC,MAAID,UAAU,wBAAwB;AAEtC,SAAOA,QAAO,WAAW,MAAM;AAC9B,IAAAA,UAASA,QAAO;AAChB,QAAIC,SAAQD,QAAO;AAInB,QACC,eACAA,YAAW,kBACVC,SAAQ,kBAAkB,MAC1BA,SAAQ,iBAAiB,GACzB;AACD;AAAA,IACD;AAEA,SAAKA,UAAS,cAAc,oBAAoB,GAAG;AAClD,WAAKA,SAAQ,WAAW,EAAG;AAC3B,MAAAD,QAAO,KAAK;AAAA,IACb;AAAA,EACD;AAEA,sBAAoB,KAAKA,OAAM;AAChC;AAGA,IAAI,iBAAiB,CAAC;AAEtB,SAAS,cAAc;AACtB,MAAI;AACH,cAAU,MAAM;AACf,iBAAW,WAAW,gBAAgB;AACrC,eAAO,OAAO;AAAA,MACf;AAAA,IACD,CAAC;AAAA,EACF,UAAE;AACD,qBAAiB,CAAC;AAAA,EACnB;AACD;AAQO,SAAS,MAAM,IAAI;AACzB,MAAI,UAAU,OAAO,CAAC;AACtB,MAAI,UAAU;AACd,MAAI;AAAA;AAAA,IAA0B;AAAA;AAE9B,MAAI,OAAO;AAEX,eAAa,MAAM;AAClB,QAAI,SAAS;AAGZ,UAAI,wBAAwB;AAE5B,UAAI;AACH,uBAAe;AACf,gBAAQ,GAAG;AAAA,MACZ,UAAE;AACD,uBAAe;AAAA,MAChB;AAEA;AAAA,IACD;AAKA,QAAI,eAAe,WAAW,GAAG;AAChC,uBAAiB,WAAW;AAAA,IAC7B;AAEA,mBAAe,KAAK,OAAO;AAAA,EAC5B,CAAC;AAED,YAAU;AAEV,SAAO;AACR;AAqBO,SAAS,KAAK,IAAI;AACxB,MAAI,CAAC,iBAAiB;AACrB,IAAE,wBAAwB;AAAA,EAC3B;AAEA,MAAI,kBAAkB,MAAM;AAC3B,IAAE,YAAY;AAAA,EACf;AAEA,MAAI,QAAQ,MAAM,OAAO;AACzB,QAAM,UAAU;AAEhB,MAAI,YAAY;AAChB,MAAIO,WAAU,MAAM,QAAQ;AAE5B,YAAU,EAAE;AAGZ,WAAS,CAACJ,SAAQ,KAAK,KAAK,MAAM,UAAU;AAC3C,IAAAA,QAAO,IAAI;AAAA,EACZ;AAEA,SAAO;AAAA,IACN,QAAQ,YAAY;AACnB,UAAI,WAAW;AACd,cAAMI;AACN;AAAA,MACD;AAEA,UAAI,CAAC,QAAQ,IAAI,KAAK,GAAG;AACxB,QAAE,eAAe;AAAA,MAClB;AAEA,kBAAY;AAEZ,YAAM,UAAU;AAGhB,eAAS,CAACJ,SAAQK,MAAK,KAAK,MAAM,SAAS;AAC1C,QAAAL,QAAO,IAAIK;AAAA,MACZ;AAOA,gBAAU,MAAM;AAEf,YAAIC,iBAAgB,oBAAI,IAAI;AAE5B,iBAASN,WAAU,MAAM,QAAQ,KAAK,GAAG;AACxC,6BAAmBA,SAAQM,cAAa;AAAA,QACzC;AAEA,0BAAkBA,cAAa;AAC/B,4BAAoB;AAAA,MACrB,CAAC;AAED,YAAM,OAAO;AACb,YAAMF;AAAA,IACP;AAAA,IACA,SAAS,MAAM;AACd,UAAI,CAAC,aAAa,QAAQ,IAAI,KAAK,GAAG;AACrC,gBAAQ,OAAO,KAAK;AACpB,cAAM,QAAQ;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;;;ACj/BO,SAAS,UAAU,KAAK,OAAO;AACrC,MAAI,OAAO;AACV,UAAM,OAAO,SAAS;AACtB,QAAI,YAAY;AAEhB,qBAAiB,MAAM;AACtB,UAAI,SAAS,kBAAkB,MAAM;AACpC,YAAI,MAAM;AAAA,MACX;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,sBAAsB,KAAK;AAC1C,MAAI,aAAa,gBAAgB,GAAG,MAAM,MAAM;AAC/C,uBAAmB,GAAG;AAAA,EACvB;AACD;AAEA,IAAI,0BAA0B;AAEvB,SAAS,0BAA0B;AACzC,MAAI,CAAC,yBAAyB;AAC7B,8BAA0B;AAC1B,aAAS;AAAA,MACR;AAAA,MACA,CAAC,QAAQ;AAGR,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC5B,cAAI,CAAC,IAAI,kBAAkB;AAC1B;AAAA,oBAAW;AAAA;AAAA,cAAoC,IAAI,OAAQ;AAAA,cAAU;AAEpE,gBAAE,SAAS;AAAA,YACZ;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,KAAK;AAAA,IACjB;AAAA,EACD;AACD;;;ACxCO,SAAS,OAAO,QAAQ,QAAQ,SAAS,2BAA2B,MAAM;AAChF,MAAI,0BAA0B;AAC7B,YAAQ;AAAA,EACT;AAEA,WAAS,QAAQ,QAAQ;AACxB,WAAO,iBAAiB,MAAM,OAAO;AAAA,EACtC;AAEA,WAAS,MAAM;AACd,aAASG,SAAQ,QAAQ;AACxB,aAAO,oBAAoBA,OAAM,OAAO;AAAA,IACzC;AAAA,EACD,CAAC;AACF;AAMO,SAAS,yBAAyB,IAAI;AAC5C,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AACtB,MAAI;AACH,WAAO,GAAG;AAAA,EACX,UAAE;AACD,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAUO,SAAS,gCAAgC,SAASC,QAAO,SAAS,WAAW,SAAS;AAC5F,UAAQ,iBAAiBA,QAAO,MAAM,yBAAyB,OAAO,CAAC;AAEvE,QAAM,OAAO,QAAQ;AACrB,MAAI,MAAM;AAGT,YAAQ,SAAS,MAAM;AACtB,WAAK;AACL,eAAS,IAAI;AAAA,IACd;AAAA,EACD,OAAO;AAEN,YAAQ,SAAS,MAAM,SAAS,IAAI;AAAA,EACrC;AAEA,0BAAwB;AACzB;;;AC1BO,SAAS,gBAAgB,MAAM;AACrC,MAAI,kBAAkB,MAAM;AAC3B,QAAI,oBAAoB,MAAM;AAC7B,MAAE,cAAc,IAAI;AAAA,IACrB;AAEA,IAAE,0BAA0B;AAAA,EAC7B;AAEA,MAAI,sBAAsB;AACzB,IAAE,mBAAmB,IAAI;AAAA,EAC1B;AACD;AAMA,SAAS,YAAYC,SAAQ,eAAe;AAC3C,MAAI,cAAc,cAAc;AAChC,MAAI,gBAAgB,MAAM;AACzB,kBAAc,OAAO,cAAc,QAAQA;AAAA,EAC5C,OAAO;AACN,gBAAY,OAAOA;AACnB,IAAAA,QAAO,OAAO;AACd,kBAAc,OAAOA;AAAA,EACtB;AACD;AAQA,SAAS,cAAc,MAAM,IAAI,MAAM;AACtC,MAAI,SAAS;AAEb,MAAI,cAAK;AAER,WAAO,WAAW,SAAS,OAAO,IAAI,kBAAkB,GAAG;AAC1D,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AAEA,MAAI,WAAW,SAAS,OAAO,IAAI,WAAW,GAAG;AAChD,YAAQ;AAAA,EACT;AAGA,MAAIA,UAAS;AAAA,IACZ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,IACb,WAAW;AAAA,IACX,GAAG,OAAO,QAAQ;AAAA,IAClB,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,IACA,GAAG,UAAU,OAAO;AAAA,IACpB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAEA,MAAI,MAAM;AACT,QAAI;AACH,oBAAcA,OAAM;AACpB,MAAAA,QAAO,KAAK;AAAA,IACb,SAASC,IAAG;AACX,qBAAeD,OAAM;AACrB,YAAMC;AAAA,IACP;AAAA,EACD,WAAW,OAAO,MAAM;AACvB,oBAAgBD,OAAM;AAAA,EACvB;AAGA,MAAI,IAAIA;AAKR,MACC,QACA,EAAE,SAAS,QACX,EAAE,aAAa,QACf,EAAE,gBAAgB,QAClB,EAAE,UAAU,EAAE;AAAA,GACb,EAAE,IAAI,sBAAsB,GAC5B;AACD,QAAI,EAAE;AACN,SAAK,OAAO,kBAAkB,MAAM,OAAO,wBAAwB,KAAK,MAAM,MAAM;AACnF,QAAE,KAAK;AAAA,IACR;AAAA,EACD;AAEA,MAAI,MAAM,MAAM;AACf,MAAE,SAAS;AAEX,QAAI,WAAW,MAAM;AACpB,kBAAY,GAAG,MAAM;AAAA,IACtB;AAGA,QACC,oBAAoB,SACnB,gBAAgB,IAAI,aAAa,MACjC,OAAO,iBAAiB,GACxB;AACD,UAAIE;AAAA;AAAA,QAAkC;AAAA;AACtC,OAACA,SAAQ,YAAY,CAAC,GAAG,KAAK,CAAC;AAAA,IAChC;AAAA,EACD;AAEA,SAAOF;AACR;AAMO,SAAS,kBAAkB;AACjC,SAAO,oBAAoB,QAAQ,CAAC;AACrC;AAKO,SAAS,SAAS,IAAI;AAC5B,QAAMA,UAAS,cAAc,eAAe,MAAM,KAAK;AACvD,oBAAkBA,SAAQ,KAAK;AAC/B,EAAAA,QAAO,WAAW;AAClB,SAAOA;AACR;AAMO,SAAS,YAAY,IAAI;AAC/B,kBAAgB,SAAS;AAEzB,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAIA,MAAIG;AAAA;AAAA,IAA+B,cAAe;AAAA;AAClD,MAAI,QAAQ,CAAC,oBAAoBA,SAAQ,mBAAmB,MAAMA,SAAQ,gBAAgB;AAE1F,MAAI,OAAO;AAEV,QAAI;AAAA;AAAA,MAA2C;AAAA;AAC/C,KAAC,QAAQ,MAAM,CAAC,GAAG,KAAK,EAAE;AAAA,EAC3B,OAAO;AAEN,WAAO,mBAAmB,EAAE;AAAA,EAC7B;AACD;AAKO,SAAS,mBAAmB,IAAI;AACtC,SAAO,cAAc,SAAS,aAAa,IAAI,KAAK;AACrD;AAOO,SAAS,gBAAgB,IAAI;AACnC,kBAAgB,aAAa;AAC7B,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AACA,SAAO,cAAc,gBAAgB,aAAa,IAAI,IAAI;AAC3D;AAGO,SAAS,aAAa,IAAI;AAChC,SAAO,cAAc,cAAc,IAAI,IAAI;AAC5C;AAOO,SAAS,YAAY,IAAI;AAC/B,QAAM,OAAO;AACb,QAAMH,UAAS,cAAc,cAAc,kBAAkB,IAAI,IAAI;AAErE,SAAO,MAAM;AACZ,mBAAeA,OAAM;AAAA,EACtB;AACD;AAOO,SAAS,eAAe,IAAI;AAClC,QAAM,OAAO;AACb,QAAMA,UAAS,cAAc,cAAc,kBAAkB,IAAI,IAAI;AAErE,SAAO,CAAC,UAAU,CAAC,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,WAAW;AAC9B,UAAI,QAAQ,OAAO;AAClB,qBAAaA,SAAQ,MAAM;AAC1B,yBAAeA,OAAM;AACrB,iBAAO,MAAS;AAAA,QACjB,CAAC;AAAA,MACF,OAAO;AACN,uBAAeA,OAAM;AACrB,eAAO,MAAS;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAMO,SAAS,OAAO,IAAI;AAC1B,SAAO,cAAc,QAAQ,IAAI,KAAK;AACvC;AAOO,SAAS,kBAAkB,MAAM,IAAI;AAC3C,MAAI;AAAA;AAAA,IAAiD;AAAA;AAGrD,MAAI,QAAQ,EAAE,QAAQ,MAAM,KAAK,OAAO,KAAK;AAE7C,UAAQ,EAAE,EAAE,KAAK,KAAK;AAEtB,QAAM,SAAS,cAAc,MAAM;AAClC,SAAK;AAIL,QAAI,MAAM,IAAK;AAEf,UAAM,MAAM;AACZ,YAAQ,EAAE;AAAA,EACX,CAAC;AACF;AAEO,SAAS,0BAA0B;AACzC,MAAI;AAAA;AAAA,IAAiD;AAAA;AAErD,gBAAc,MAAM;AAEnB,aAAS,SAAS,QAAQ,EAAE,GAAG;AAC9B,YAAM,KAAK;AAEX,UAAIA,UAAS,MAAM;AAInB,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,0BAAkBA,SAAQ,WAAW;AAAA,MACtC;AAEA,UAAI,SAASA,OAAM,GAAG;AACrB,sBAAcA,OAAM;AAAA,MACrB;AAEA,YAAM,MAAM;AAAA,IACb;AAAA,EACD,CAAC;AACF;AAMO,SAAS,aAAa,IAAI;AAChC,SAAO,cAAc,QAAQ,kBAAkB,IAAI,IAAI;AACxD;AAMO,SAAS,cAAc,IAAIG,SAAQ,GAAG;AAC5C,SAAO,cAAc,gBAAgBA,QAAO,IAAI,IAAI;AACrD;AAQO,SAAS,gBAAgB,IAAI,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG;AACzE,UAAQ,UAAU,MAAM,OAAO,CAAC,WAAW;AAC1C,kBAAc,eAAe,MAAM,GAAG,GAAG,OAAO,IAAI,GAAG,CAAC,GAAG,IAAI;AAAA,EAChE,CAAC;AACF;AASO,SAAS,yBAAyB,IAAI,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG;AAClF,MAAI;AAAA;AAAA,IAA8B;AAAA;AAClC,MAAI,WAAW,MAAM,SAAS,KAAK,SAAS,SAAS;AAErD,MAAI,SAAU,OAAM,UAAU,IAAI;AAElC,UAAQ,UAAU,MAAM,OAAO,CAAC,WAAW;AAC1C,kBAAc,QAAQ,MAAM,GAAG,GAAG,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK;AACzD,QAAI,SAAU,OAAM,UAAU,IAAI;AAAA,EACnC,CAAC;AACF;AAMO,SAAS,MAAM,IAAIA,SAAQ,GAAG;AACpC,MAAIH,UAAS,cAAc,eAAeG,QAAO,IAAI,IAAI;AACzD,MAAI,cAAK;AACR,IAAAH,QAAO,YAAY;AAAA,EACpB;AACA,SAAOA;AACR;AAKO,SAAS,OAAO,IAAI;AAC1B,SAAO,cAAc,gBAAgB,kBAAkB,IAAI,IAAI;AAChE;AAKO,SAAS,wBAAwBA,SAAQ;AAC/C,MAAII,YAAWJ,QAAO;AACtB,MAAII,cAAa,MAAM;AACtB,UAAM,+BAA+B;AACrC,UAAM,oBAAoB;AAC1B,6BAAyB,IAAI;AAC7B,wBAAoB,IAAI;AACxB,QAAI;AACH,MAAAA,UAAS,KAAK,IAAI;AAAA,IACnB,UAAE;AACD,+BAAyB,4BAA4B;AACrD,0BAAoB,iBAAiB;AAAA,IACtC;AAAA,EACD;AACD;AAOO,SAAS,wBAAwB,QAAQ,aAAa,OAAO;AACnE,MAAIJ,UAAS,OAAO;AACpB,SAAO,QAAQ,OAAO,OAAO;AAE7B,SAAOA,YAAW,MAAM;AACvB,UAAM,aAAaA,QAAO;AAE1B,QAAI,eAAe,MAAM;AACxB,+BAAyB,MAAM;AAC9B,mBAAW,MAAM,cAAc;AAAA,MAChC,CAAC;AAAA,IACF;AAEA,QAAIK,QAAOL,QAAO;AAElB,SAAKA,QAAO,IAAI,iBAAiB,GAAG;AAEnC,MAAAA,QAAO,SAAS;AAAA,IACjB,OAAO;AACN,qBAAeA,SAAQ,UAAU;AAAA,IAClC;AAEA,IAAAA,UAASK;AAAA,EACV;AACD;AAMO,SAAS,8BAA8B,QAAQ;AACrD,MAAIL,UAAS,OAAO;AAEpB,SAAOA,YAAW,MAAM;AACvB,QAAIK,QAAOL,QAAO;AAClB,SAAKA,QAAO,IAAI,mBAAmB,GAAG;AACrC,qBAAeA,OAAM;AAAA,IACtB;AACA,IAAAA,UAASK;AAAA,EACV;AACD;AAOO,SAAS,eAAeL,SAAQ,aAAa,MAAM;AACzD,MAAI,UAAU;AAEd,OACE,eAAeA,QAAO,IAAI,iBAAiB,MAC5CA,QAAO,gBAAgB,QACvBA,QAAO,cAAc,MACpB;AACD;AAAA,MAAkBA,QAAO;AAAA;AAAA,MAA0CA,QAAO;AAAA,IAAU;AACpF,cAAU;AAAA,EACX;AAEA,0BAAwBA,SAAQ,cAAc,CAAC,OAAO;AACtD,mBAAiBA,SAAQ,CAAC;AAC1B,oBAAkBA,SAAQ,SAAS;AAEnC,MAAI,cAAcA,QAAO;AAEzB,MAAI,gBAAgB,MAAM;AACzB,eAAW,cAAc,aAAa;AACrC,iBAAW,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,0BAAwBA,OAAM;AAE9B,MAAI,SAASA,QAAO;AAGpB,MAAI,WAAW,QAAQ,OAAO,UAAU,MAAM;AAC7C,kBAAcA,OAAM;AAAA,EACrB;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAIA,EAAAA,QAAO,OACNA,QAAO,OACPA,QAAO,WACPA,QAAO,MACPA,QAAO,OACPA,QAAO,KACPA,QAAO,cACPA,QAAO,YACPA,QAAO,KACN;AACH;AAOO,SAAS,kBAAkB,MAAM,KAAK;AAC5C,SAAO,SAAS,MAAM;AAErB,QAAIK,QAAO,SAAS,MAAM;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAEnF,SAAK,OAAO;AACZ,WAAOA;AAAA,EACR;AACD;AAOO,SAAS,cAAcL,SAAQ;AACrC,MAAI,SAASA,QAAO;AACpB,MAAI,OAAOA,QAAO;AAClB,MAAIK,QAAOL,QAAO;AAElB,MAAI,SAAS,KAAM,MAAK,OAAOK;AAC/B,MAAIA,UAAS,KAAM,CAAAA,MAAK,OAAO;AAE/B,MAAI,WAAW,MAAM;AACpB,QAAI,OAAO,UAAUL,QAAQ,QAAO,QAAQK;AAC5C,QAAI,OAAO,SAASL,QAAQ,QAAO,OAAO;AAAA,EAC3C;AACD;AAYO,SAAS,aAAaA,SAAQ,UAAU,UAAU,MAAM;AAE9D,MAAI,cAAc,CAAC;AAEnB,iBAAeA,SAAQ,aAAa,IAAI;AAExC,sBAAoB,aAAa,MAAM;AACtC,QAAI,QAAS,gBAAeA,OAAM;AAClC,QAAI,SAAU,UAAS;AAAA,EACxB,CAAC;AACF;AAMO,SAAS,oBAAoB,aAAa,IAAI;AACpD,MAAI,YAAY,YAAY;AAC5B,MAAI,YAAY,GAAG;AAClB,QAAI,QAAQ,MAAM,EAAE,aAAa,GAAG;AACpC,aAAS,cAAc,aAAa;AACnC,iBAAW,IAAI,KAAK;AAAA,IACrB;AAAA,EACD,OAAO;AACN,OAAG;AAAA,EACJ;AACD;AAOO,SAAS,eAAeA,SAAQ,aAAa,OAAO;AAC1D,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAEZ,MAAIA,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,oBAAY,KAAK,UAAU;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAEA,MAAIM,SAAQN,QAAO;AAEnB,SAAOM,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eACFA,OAAM,IAAI,wBAAwB;AAAA;AAAA;AAAA,KAIjCA,OAAM,IAAI,mBAAmB,MAAMN,QAAO,IAAI,kBAAkB;AAInE,mBAAeM,QAAO,aAAa,cAAc,QAAQ,KAAK;AAC9D,IAAAA,SAAQC;AAAA,EACT;AACD;AAOO,SAAS,cAAcP,SAAQ;AACrC,kBAAgBA,SAAQ,IAAI;AAC7B;AAMA,SAAS,gBAAgBA,SAAQ,OAAO;AACvC,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAMZ,OAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,sBAAkBA,SAAQ,KAAK;AAC/B,oBAAgBA,OAAM;AAAA,EACvB;AAEA,MAAIM,SAAQN,QAAO;AAEnB,SAAOM,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,oBAAgBA,QAAO,cAAc,QAAQ,KAAK;AAClD,IAAAA,SAAQC;AAAA,EACT;AAEA,MAAIP,QAAO,gBAAgB,MAAM;AAChC,eAAW,cAAcA,QAAO,aAAa;AAC5C,UAAI,WAAW,aAAa,OAAO;AAClC,mBAAW,GAAG;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,QAAQA;AAAA;AAAA,EAAgC;AAAA,GAAgB;AACvE,UAAQA,QAAO,IAAI,eAAe;AACnC;AAMO,SAAS,YAAYA,SAAQ,UAAU;AAC7C,MAAI,OAAOA,QAAO;AAClB,MAAI,MAAMA,QAAO;AAEjB,SAAO,SAAS,MAAM;AAErB,QAAIK,QAAO,SAAS,MAAM;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAEnF,aAAS,OAAO,IAAI;AACpB,WAAOA;AAAA,EACR;AACD;;;ACzrBO,IAAI,mBAAmB;AAO9B,SAAS,gBAAgB,IAAI;AAC5B,MAAI,4BAA4B;AAEhC,MAAI;AACH,uBAAmB,oBAAI,IAAI;AAE3B,YAAQ,EAAE;AAEV,QAAI,8BAA8B,MAAM;AACvC,eAAS,UAAU,kBAAkB;AACpC,kCAA0B,IAAI,MAAM;AAAA,MACrC;AAAA,IACD;AAEA,WAAO;AAAA,EACR,UAAE;AACD,uBAAmB;AAAA,EACpB;AACD;AAQO,SAAS,yBAAyB,IAAI;AAC5C,WAAS,UAAU,gBAAgB,EAAE,GAAG;AACvC,iBAAa,QAAQ,OAAO,CAAC;AAAA,EAC9B;AACD;;;ACMO,IAAI,qBAAqB;AAGzB,SAAS,uBAAuB,OAAO;AAC7C,uBAAqB;AACtB;AAEO,IAAI,uBAAuB;AAG3B,SAAS,yBAAyB,OAAO;AAC/C,yBAAuB;AACxB;AAGO,IAAI,kBAAkB;AAEtB,IAAI,aAAa;AAGjB,SAAS,oBAAoB,UAAU;AAC7C,oBAAkB;AACnB;AAGO,IAAI,gBAAgB;AAGpB,SAAS,kBAAkBG,SAAQ;AACzC,kBAAgBA;AACjB;AAOO,IAAI,kBAAkB;AAGtB,SAAS,oBAAoB,OAAO;AAC1C,MAAI,oBAAoB,SAAS,CAAC,oBAAoB,gBAAgB,IAAI,aAAa,IAAI;AAC1F,QAAI,oBAAoB,MAAM;AAC7B,wBAAkB,CAAC,KAAK;AAAA,IACzB,OAAO;AACN,sBAAgB,KAAK,KAAK;AAAA,IAC3B;AAAA,EACD;AACD;AAQA,IAAI,WAAW;AAEf,IAAI,eAAe;AAOZ,IAAI,mBAAmB;AAGvB,SAAS,qBAAqB,OAAO;AAC3C,qBAAmB;AACpB;AAMO,IAAI,gBAAgB;AAG3B,IAAI,eAAe;AAEZ,IAAI,iBAAiB;AAGrB,SAAS,mBAAmB,OAAO;AACzC,mBAAiB;AAClB;AAEO,SAAS,0BAA0B;AACzC,SAAO,EAAE;AACV;AAQO,SAAS,SAAS,UAAU;AAClC,MAAIC,SAAQ,SAAS;AAErB,OAAKA,SAAQ,WAAW,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,MAAIA,SAAQ,SAAS;AACpB,aAAS,KAAK,CAAC;AAAA,EAChB;AAEA,OAAKA,SAAQ,iBAAiB,GAAG;AAChC,QAAI,eAAe,SAAS;AAE5B,QAAI,iBAAiB,MAAM;AAC1B,UAAI,SAAS,aAAa;AAE1B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAI,aAAa,aAAa,CAAC;AAE/B,YAAI;AAAA;AAAA,UAAiC;AAAA,QAAW,GAAG;AAClD;AAAA;AAAA,YAAuC;AAAA,UAAW;AAAA,QACnD;AAEA,YAAI,WAAW,KAAK,SAAS,IAAI;AAChC,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,SACEA,SAAQ,eAAe;AAAA;AAAA,IAGxB,iBAAiB,MAChB;AACD,wBAAkB,UAAU,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,SAAO;AACR;AAOA,SAAS,2CAA2C,QAAQD,SAAQ,OAAO,MAAM;AAChF,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,MAAI,CAAC,mBAAmB,iBAAiB,SAAS,MAAM,GAAG;AAC1D;AAAA,EACD;AAEA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC;AAE1B,SAAK,SAAS,IAAI,aAAa,GAAG;AACjC;AAAA;AAAA,QAAmE;AAAA,QAAWA;AAAA,QAAQ;AAAA,MAAK;AAAA,IAC5F,WAAWA,YAAW,UAAU;AAC/B,UAAI,MAAM;AACT,0BAAkB,UAAU,KAAK;AAAA,MAClC,YAAY,SAAS,IAAI,WAAW,GAAG;AACtC,0BAAkB,UAAU,WAAW;AAAA,MACxC;AACA;AAAA;AAAA,QAAuC;AAAA,MAAS;AAAA,IACjD;AAAA,EACD;AACD;AAGO,SAAS,gBAAgB,UAAU;AACzC,MAAI,gBAAgB;AACpB,MAAI,wBAAwB;AAC5B,MAAI,4BAA4B;AAChC,MAAI,oBAAoB;AACxB,MAAI,mBAAmB;AACvB,MAAI,6BAA6B;AACjC,MAAI,sBAAsB;AAC1B,MAAI,0BAA0B;AAE9B,MAAIC,SAAQ,SAAS;AAErB;AAAA,EAA0C;AAC1C,iBAAe;AACf,qBAAmB;AACnB,qBAAmBA,UAAS,gBAAgB,kBAAkB,IAAI,WAAW;AAE7E,oBAAkB;AAClB,wBAAsB,SAAS,GAAG;AAClC,eAAa;AACb,mBAAiB,EAAE;AAEnB,MAAI,SAAS,OAAO,MAAM;AACzB,6BAAyB,MAAM;AACC,MAAC,SAAS,GAAI,MAAM,cAAc;AAAA,IAClE,CAAC;AAED,aAAS,KAAK;AAAA,EACf;AAEA,MAAI;AACH,aAAS,KAAK;AACd,QAAI;AAAA;AAAA,MAA8B,SAAS;AAAA;AAC3C,QAAI,SAAS,GAAG;AAChB,QAAI,OAAO,SAAS;AAEpB,QAAI,aAAa,MAAM;AACtB,UAAI;AAEJ,uBAAiB,UAAU,YAAY;AAEvC,UAAI,SAAS,QAAQ,eAAe,GAAG;AACtC,aAAK,SAAS,eAAe,SAAS;AACtC,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACrC,eAAK,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA,QACpC;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAO;AAAA,MACxB;AAEA,UAAI,sBAAsB,gBAAgB,MAAM,SAAS,IAAI,eAAe,GAAG;AAC9E,aAAK,IAAI,cAAc,IAAI,KAAK,QAAQ,KAAK;AAC5C,WAAC,KAAK,CAAC,EAAE,cAAc,CAAC,GAAG,KAAK,QAAQ;AAAA,QACzC;AAAA,MACD;AAAA,IACD,WAAW,SAAS,QAAQ,eAAe,KAAK,QAAQ;AACvD,uBAAiB,UAAU,YAAY;AACvC,WAAK,SAAS;AAAA,IACf;AAKA,QACC,SAAS,KACT,qBAAqB,QACrB,CAAC,cACD,SAAS,SACR,SAAS,KAAK,UAAU,cAAc,YAAY,GAClD;AACD,WAAK,IAAI,GAAG;AAAA,MAA6B,iBAAkB,QAAQ,KAAK;AACvE;AAAA,UACC,iBAAiB,CAAC;AAAA;AAAA,UACK;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAMA,QAAI,sBAAsB,QAAQ,sBAAsB,UAAU;AACjE;AAEA,UAAI,qBAAqB,MAAM;AAC9B,YAAI,8BAA8B,MAAM;AACvC,sCAA4B;AAAA,QAC7B,OAAO;AACN,oCAA0B,KAAK;AAAA,UAA4B,gBAAiB;AAAA,QAC7E;AAAA,MACD;AAAA,IACD;AAEA,SAAK,SAAS,IAAI,iBAAiB,GAAG;AACrC,eAAS,KAAK;AAAA,IACf;AAEA,WAAO;AAAA,EACR,SAAS,OAAO;AACf,WAAO,aAAa,KAAK;AAAA,EAC1B,UAAE;AACD,aAAS,KAAK;AACd,eAAW;AACX,mBAAe;AACf,uBAAmB;AACnB,sBAAkB;AAClB,sBAAkB;AAClB,0BAAsB,0BAA0B;AAChD,iBAAa;AACb,qBAAiB;AAAA,EAClB;AACD;AAQA,SAAS,gBAAgB,QAAQ,YAAY;AAC5C,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,MAAM;AACvB,QAAIC,SAAQ,SAAS,KAAK,WAAW,MAAM;AAC3C,QAAIA,WAAU,IAAI;AACjB,UAAI,aAAa,UAAU,SAAS;AACpC,UAAI,eAAe,GAAG;AACrB,oBAAY,WAAW,YAAY;AAAA,MACpC,OAAO;AAEN,kBAAUA,MAAK,IAAI,UAAU,UAAU;AACvC,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAIA,MACC,cAAc,SACb,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA,GAI5B,aAAa,QAAQ,CAAC,SAAS,SAAS,UAAU,IAClD;AACD,sBAAkB,YAAY,WAAW;AAGzC,SAAK,WAAW,IAAI,eAAe,GAAG;AACrC,iBAAW,KAAK;AAChB,iBAAW,KAAK,CAAC;AAAA,IAClB;AAEA;AAAA;AAAA,MAAiD;AAAA,IAAW;AAC5D;AAAA;AAAA,MAA0C;AAAA,MAAa;AAAA,IAAC;AAAA,EACzD;AACD;AAOO,SAAS,iBAAiB,QAAQ,aAAa;AACrD,MAAI,eAAe,OAAO;AAC1B,MAAI,iBAAiB,KAAM;AAE3B,WAAS,IAAI,aAAa,IAAI,aAAa,QAAQ,KAAK;AACvD,oBAAgB,QAAQ,aAAa,CAAC,CAAC;AAAA,EACxC;AACD;AAMO,SAAS,cAAcF,SAAQ;AACrC,MAAIC,SAAQD,QAAO;AAEnB,OAAKC,SAAQ,eAAe,GAAG;AAC9B;AAAA,EACD;AAEA,oBAAkBD,SAAQ,KAAK;AAE/B,MAAI,kBAAkB;AACtB,MAAI,sBAAsB;AAE1B,kBAAgBA;AAChB,uBAAqB;AAErB,MAAI,cAAK;AACR,QAAI,wBAAwB;AAC5B,uCAAmCA,QAAO,kBAAkB;AAC5D,QAAI;AAAA;AAAA,MAAqC;AAAA;AAEzC,kBAAcA,QAAO,aAAa,SAAS;AAAA,EAC5C;AAEA,MAAI;AACH,SAAKC,SAAQ,kBAAkB,GAAG;AACjC,oCAA8BD,OAAM;AAAA,IACrC,OAAO;AACN,8BAAwBA,OAAM;AAAA,IAC/B;AAEA,4BAAwBA,OAAM;AAC9B,QAAIG,YAAW,gBAAgBH,OAAM;AACrC,IAAAA,QAAO,WAAW,OAAOG,cAAa,aAAaA,YAAW;AAC9D,IAAAH,QAAO,KAAK;AAIZ,QAAI,gBAAO,sBAAsBA,QAAO,IAAI,WAAW,KAAKA,QAAO,SAAS,MAAM;AACjF,eAAS,OAAOA,QAAO,MAAM;AAC5B,YAAI,IAAI,mBAAmB;AAC1B,cAAI,KAAK,wBAAwB;AACjC,cAAI,oBAAoB;AAAA,QACzB;AAAA,MACD;AAAA,IACD;AAAA,EACD,UAAE;AACD,yBAAqB;AACrB,oBAAgB;AAEhB,QAAI,cAAK;AACR,yCAAmC,qBAAqB;AACxD,oBAAc,cAAc;AAAA,IAC7B;AAAA,EACD;AACD;AAMA,eAAsB,OAAO;AAC5B,MAAI,iBAAiB;AACpB,WAAO,IAAI,QAAQ,CAAC,MAAM;AAIzB,4BAAsB,MAAM,EAAE,CAAC;AAC/B,iBAAW,MAAM,EAAE,CAAC;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,QAAQ;AAItB,YAAU;AACX;AAQO,SAAS,UAAU;AACzB,SAAO,MAAM,OAAO,EAAE,QAAQ;AAC/B;AAOO,SAAS,IAAI,QAAQ;AAC3B,MAAIC,SAAQ,OAAO;AACnB,MAAI,cAAcA,SAAQ,aAAa;AAEvC,oBAAkB,IAAI,MAAM;AAG5B,MAAI,oBAAoB,QAAQ,CAAC,YAAY;AAI5C,QAAI,YAAY,kBAAkB,SAAS,cAAc,IAAI,eAAe;AAE5E,QAAI,CAAC,aAAa,CAAC,iBAAiB,SAAS,MAAM,GAAG;AACrD,UAAI,OAAO,gBAAgB;AAE3B,WAAK,gBAAgB,IAAI,0BAA0B,GAAG;AAErD,YAAI,OAAO,KAAK,cAAc;AAC7B,iBAAO,KAAK;AAKZ,cAAI,aAAa,QAAQ,SAAS,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACxE;AAAA,UACD,WAAW,aAAa,MAAM;AAC7B,uBAAW,CAAC,MAAM;AAAA,UACnB,WAAW,CAAC,SAAS,SAAS,MAAM,GAAG;AACtC,qBAAS,KAAK,MAAM;AAAA,UACrB;AAAA,QACD;AAAA,MACD,OAAO;AAGN,SAAC,gBAAgB,SAAS,CAAC,GAAG,KAAK,MAAM;AAEzC,YAAI,YAAY,OAAO;AAEvB,YAAI,cAAc,MAAM;AACvB,iBAAO,YAAY,CAAC,eAAe;AAAA,QACpC,WAAW,CAAC,UAAU,SAAS,eAAe,GAAG;AAChD,oBAAU,KAAK,eAAe;AAAA,QAC/B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,cAAK;AAeR,0BAAsB,OAAO,MAAM;AAEnC,QACC,qBACA,CAAC,cACD,wBAAwB,QACxB,oBAAoB,QACpB,oBAAoB,aAAa,iBAChC;AAED,UAAI,OAAO,OAAO;AACjB,eAAO,MAAM;AAAA,MACd,OAAO;AACN,YAAIG,SAAQ,UAAU,WAAW;AAEjC,YAAIA,QAAO;AACV,cAAI,QAAQ,oBAAoB,QAAQ,IAAI,MAAM;AAElD,cAAI,UAAU,QAAW;AACxB,oBAAQ,EAAE,QAAQ,CAAC,EAAE;AACrB,gCAAoB,QAAQ,IAAI,QAAQ,KAAK;AAAA,UAC9C;AAEA,cAAI,OAAO,MAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AAI/C,cAAIA,OAAM,UAAU,MAAM,OAAO;AAChC,kBAAM,OAAO,KAAKA,MAAK;AAAA,UACxB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,sBAAsB;AACzB,QAAI,WAAW,IAAI,MAAM,GAAG;AAC3B,aAAO,WAAW,IAAI,MAAM;AAAA,IAC7B;AAEA,QAAI,YAAY;AACf,UAAIC;AAAA;AAAA,QAAkC;AAAA;AAEtC,UAAI,QAAQA,SAAQ;AAIpB,WACGA,SAAQ,IAAI,WAAW,KAAKA,SAAQ,cAAc,QACpD,sBAAsBA,QAAO,GAC5B;AACD,gBAAQ,gBAAgBA,QAAO;AAAA,MAChC;AAEA,iBAAW,IAAIA,UAAS,KAAK;AAE7B,aAAO;AAAA,IACR;AAAA,EACD,WAAW,cAAc,CAAC,cAAc,IAAI,MAAM,GAAG;AACpD,IAAAA;AAAA,IAAkC;AAElC,QAAI,SAASA,QAAO,GAAG;AACtB,qBAAeA,QAAO;AAAA,IACvB;AAEA,QAAI,sBAAsB,gBAAgB,MAAMA,SAAQ,IAAI,eAAe,GAAG;AAC7E,gBAAUA,QAAO;AAAA,IAClB;AAAA,EACD;AAEA,MAAI,cAAc,IAAI,MAAM,GAAG;AAC9B,WAAO,aAAa,IAAI,MAAM;AAAA,EAC/B;AAEA,OAAK,OAAO,IAAI,iBAAiB,GAAG;AACnC,UAAM,OAAO;AAAA,EACd;AAEA,SAAO,OAAO;AACf;AAOA,SAAS,UAAUA,UAAS;AAC3B,MAAIA,SAAQ,SAAS,KAAM;AAE3B,EAAAA,SAAQ,KAAK;AAEb,aAAW,OAAOA,SAAQ,MAAM;AAC/B,KAAC,IAAI,cAAc,CAAC,GAAG,KAAKA,QAAO;AAEnC,SAAK,IAAI,IAAI,aAAa,MAAM,IAAI,IAAI,eAAe,GAAG;AACzD;AAAA;AAAA,QAAkC;AAAA,MAAI;AAAA,IACvC;AAAA,EACD;AACD;AAGA,SAAS,sBAAsBA,UAAS;AACvC,MAAIA,SAAQ,MAAM,cAAe,QAAO;AACxC,MAAIA,SAAQ,SAAS,KAAM,QAAO;AAElC,aAAW,OAAOA,SAAQ,MAAM;AAC/B,QAAI,WAAW,IAAI,GAAG,GAAG;AACxB,aAAO;AAAA,IACR;AAEA,SAAK,IAAI,IAAI,aAAa,KAAK;AAAA;AAAA,MAA8C;AAAA,IAAI,GAAG;AACnF,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAQO,SAAS,SAAS,QAAQ;AAChC,SAAO,UAAU,IAAI,MAAM;AAC5B;AAkBO,SAAS,QAAQ,IAAI;AAC3B,MAAI,sBAAsB;AAC1B,MAAI;AACH,iBAAa;AACb,WAAO,GAAG;AAAA,EACX,UAAE;AACD,iBAAa;AAAA,EACd;AACD;AAEA,IAAM,cAAc,EAAE,QAAQ,cAAc;AAOrC,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,SAAO,IAAK,OAAO,IAAI,cAAe;AACvC;AAOO,SAAS,oBAAoB,KAAK,MAAM;AAE9C,MAAI,SAAS,CAAC;AAEd,WAAS,OAAO,KAAK;AACpB,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACtB;AAAA,EACD;AAEA,WAAS,UAAU,OAAO,sBAAsB,GAAG,GAAG;AACrD,QAAI,OAAO,qBAAqB,KAAK,KAAK,MAAM,KAAK,CAAC,KAAK,SAAS,MAAM,GAAG;AAC5E,aAAO,MAAM,IAAI,IAAI,MAAM;AAAA,IAC5B;AAAA,EACD;AAEA,SAAO;AACR;AAQO,SAAS,gBAAgB,OAAO;AACtC,MAAI,OAAO,UAAU,YAAY,CAAC,SAAS,iBAAiB,aAAa;AACxE;AAAA,EACD;AAEA,MAAI,gBAAgB,OAAO;AAC1B,cAAU,KAAK;AAAA,EAChB,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AACjC,aAAS,OAAO,OAAO;AACtB,YAAM,OAAO,MAAM,GAAG;AACtB,UAAI,OAAO,SAAS,YAAY,QAAQ,gBAAgB,MAAM;AAC7D,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AASO,SAAS,UAAU,OAAO,UAAU,oBAAI,IAAI,GAAG;AACrD,MACC,OAAO,UAAU,YACjB,UAAU;AAAA,EAEV,EAAE,iBAAiB,gBACnB,CAAC,QAAQ,IAAI,KAAK,GACjB;AACD,YAAQ,IAAI,KAAK;AAGjB,QAAI,iBAAiB,MAAM;AAC1B,YAAM,QAAQ;AAAA,IACf;AACA,aAAS,OAAO,OAAO;AACtB,UAAI;AACH,kBAAU,MAAM,GAAG,GAAG,OAAO;AAAA,MAC9B,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AACA,UAAM,QAAQ,iBAAiB,KAAK;AACpC,QACC,UAAU,OAAO,aACjB,UAAU,MAAM,aAChB,UAAU,IAAI,aACd,UAAU,IAAI,aACd,UAAU,KAAK,WACd;AACD,YAAM,cAAc,gBAAgB,KAAK;AACzC,eAAS,OAAO,aAAa;AAC5B,cAAMC,OAAM,YAAY,GAAG,EAAE;AAC7B,YAAIA,MAAK;AACR,cAAI;AACH,YAAAA,KAAI,KAAK,KAAK;AAAA,UACf,SAAS,GAAG;AAAA,UAEZ;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;ACjxBA,IAAM,4BAA4B;AAO3B,SAAS,MAAM,OAAO;AAE5B,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,OAAO;AACzE,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,iBAAiB,KAAK;AAExC,MAAI,cAAc,oBAAoB,cAAc,iBAAiB;AACpE,WAAO;AAAA,EACR;AAGA,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,mBAAmB,SAAS,KAAK;AACrC,MAAI,UAAU,MAAO,CAAC;AAEtB,MAAIC,SAAQ,gBAAO,oBAAoB,UAAU,YAAY,IAAI;AACjE,MAAI,iBAAiB;AAOrB,MAAI,cAAc,CAAC,OAAO;AACzB,QAAI,mBAAmB,gBAAgB;AACtC,aAAO,GAAG;AAAA,IACX;AAIA,QAAI,WAAW;AACf,QAAIC,WAAU;AAEd,wBAAoB,IAAI;AACxB,uBAAmB,cAAc;AAEjC,QAAI,SAAS,GAAG;AAEhB,wBAAoB,QAAQ;AAC5B,uBAAmBA,QAAO;AAE1B,WAAO;AAAA,EACR;AAEA,MAAI,kBAAkB;AAGrB,YAAQ,IAAI,UAAU;AAAA;AAAA,MAA6B,MAAO;AAAA,MAAQD;AAAA,IAAK,CAAC;AACxE,QAAI,cAAK;AACR;AAAA,MAA4B;AAAA;AAAA,QAAwC;AAAA,MAAM;AAAA,IAC3E;AAAA,EACD;AAGA,MAAI,OAAO;AACX,MAAI,WAAW;AAEf,WAAS,YAAY,UAAU;AAC9B,QAAI,SAAU;AACd,eAAW;AACX,WAAO;AAEP,QAAI,SAAS,GAAG,IAAI,UAAU;AAG9B,eAAW,CAAC,MAAME,OAAM,KAAK,SAAS;AACrC,UAAIA,SAAQ,UAAU,MAAM,IAAI,CAAC;AAAA,IAClC;AACA,eAAW;AAAA,EACZ;AAEA,SAAO,IAAI;AAAA;AAAA,IAA0B;AAAA,IAAQ;AAAA,MAC5C,eAAe,GAAG,MAAM,YAAY;AACnC,YACC,EAAE,WAAW,eACb,WAAW,iBAAiB,SAC5B,WAAW,eAAe,SAC1B,WAAW,aAAa,OACvB;AAKD,UAAE,wBAAwB;AAAA,QAC3B;AACA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAM,QAAW;AACpB,cAAI,YAAY,MAAM;AACrB,gBAAIC,KAAI,MAAO,WAAW,OAAOH,MAAK;AACtC,oBAAQ,IAAI,MAAMG,EAAC;AACnB,gBAAI,gBAAO,OAAO,SAAS,UAAU;AACpC,kBAAIA,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AACA,mBAAOA;AAAA,UACR,CAAC;AAAA,QACF,OAAO;AACN,cAAI,GAAG,WAAW,OAAO,IAAI;AAAA,QAC9B;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,eAAe,QAAQ,MAAM;AAC5B,YAAI,IAAI,QAAQ,IAAI,IAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAI,QAAQ,QAAQ;AACnB,kBAAMA,KAAI,YAAY,MAAM,MAAO,eAAeH,MAAK,CAAC;AACxD,oBAAQ,IAAI,MAAMG,EAAC;AACnB,sBAAU,OAAO;AAEjB,gBAAI,cAAK;AACR,kBAAIA,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AAAA,UACD;AAAA,QACD,OAAO;AACN,cAAI,GAAG,aAAa;AACpB,oBAAU,OAAO;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM,UAAU;AAC3B,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,gBAAO,SAAS,mBAAmB;AACtC,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,SAAS,QAAQ;AAGrB,YAAI,MAAM,WAAc,CAAC,UAAU,eAAe,QAAQ,IAAI,GAAG,WAAW;AAC3E,cAAI,YAAY,MAAM;AACrB,gBAAI,IAAI,MAAM,SAAS,OAAO,IAAI,IAAI,aAAa;AACnD,gBAAIA,KAAI,MAAO,GAAGH,MAAK;AAEvB,gBAAI,cAAK;AACR,kBAAIG,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AAEA,mBAAOA;AAAA,UACR,CAAC;AAED,kBAAQ,IAAI,MAAM,CAAC;AAAA,QACpB;AAEA,YAAI,MAAM,QAAW;AACpB,cAAI,IAAI,IAAI,CAAC;AACb,iBAAO,MAAM,gBAAgB,SAAY;AAAA,QAC1C;AAEA,eAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,MAC1C;AAAA,MAEA,yBAAyB,QAAQ,MAAM;AACtC,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAE9D,YAAI,cAAc,WAAW,YAAY;AACxC,cAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,cAAI,EAAG,YAAW,QAAQ,IAAI,CAAC;AAAA,QAChC,WAAW,eAAe,QAAW;AACpC,cAAID,UAAS,QAAQ,IAAI,IAAI;AAC7B,cAAIE,SAAQF,SAAQ;AAEpB,cAAIA,YAAW,UAAaE,WAAU,eAAe;AACpD,mBAAO;AAAA,cACN,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,OAAAA;AAAA,cACA,UAAU;AAAA,YACX;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAM;AACjB,YAAI,SAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAO,MAAM,UAAa,EAAE,MAAM,iBAAkB,QAAQ,IAAI,QAAQ,IAAI;AAEhF,YACC,MAAM,UACL,kBAAkB,SAAS,CAAC,OAAO,eAAe,QAAQ,IAAI,GAAG,WACjE;AACD,cAAI,MAAM,QAAW;AACpB,gBAAI,YAAY,MAAM;AACrB,kBAAI,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI;AACpC,kBAAID,KAAI,MAAO,GAAGH,MAAK;AAEvB,kBAAI,cAAK;AACR,oBAAIG,IAAG,UAAU,MAAM,IAAI,CAAC;AAAA,cAC7B;AAEA,qBAAOA;AAAA,YACR,CAAC;AAED,oBAAQ,IAAI,MAAM,CAAC;AAAA,UACpB;AAEA,cAAIC,SAAQ,IAAI,CAAC;AACjB,cAAIA,WAAU,eAAe;AAC5B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQ,MAAMA,QAAO,UAAU;AAClC,YAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,YAAI,MAAM,QAAQ;AAGlB,YAAI,oBAAoB,SAAS,UAAU;AAC1C,mBAAS,IAAIA,QAAO;AAAA,UAAmC,EAAG,GAAG,KAAK,GAAG;AACpE,gBAAI,UAAU,QAAQ,IAAI,IAAI,EAAE;AAChC,gBAAI,YAAY,QAAW;AAC1B,kBAAI,SAAS,aAAa;AAAA,YAC3B,WAAW,KAAK,QAAQ;AAIvB,wBAAU,YAAY,MAAM,MAAO,eAAeJ,MAAK,CAAC;AACxD,sBAAQ,IAAI,IAAI,IAAI,OAAO;AAE3B,kBAAI,cAAK;AACR,oBAAI,SAAS,UAAU,MAAM,CAAC,CAAC;AAAA,cAChC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAMA,YAAI,MAAM,QAAW;AACpB,cAAI,CAAC,OAAO,eAAe,QAAQ,IAAI,GAAG,UAAU;AACnD,gBAAI,YAAY,MAAM,MAAO,QAAWA,MAAK,CAAC;AAE9C,gBAAI,cAAK;AACR,kBAAI,GAAG,UAAU,MAAM,IAAI,CAAC;AAAA,YAC7B;AACA,gBAAI,GAAG,MAAMI,MAAK,CAAC;AAEnB,oBAAQ,IAAI,MAAM,CAAC;AAAA,UACpB;AAAA,QACD,OAAO;AACN,gBAAM,EAAE,MAAM;AAEd,cAAI,IAAI,YAAY,MAAM,MAAMA,MAAK,CAAC;AACtC,cAAI,GAAG,CAAC;AAAA,QACT;AAEA,YAAI,aAAa,QAAQ,yBAAyB,QAAQ,IAAI;AAG9D,YAAI,YAAY,KAAK;AACpB,qBAAW,IAAI,KAAK,UAAUA,MAAK;AAAA,QACpC;AAEA,YAAI,CAAC,KAAK;AAKT,cAAI,oBAAoB,OAAO,SAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAO,IAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG,GAAG;AACrC,kBAAI,IAAI,IAAI,CAAC;AAAA,YACd;AAAA,UACD;AAEA,oBAAU,OAAO;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,QAAQ;AACf,YAAI,OAAO;AAEX,YAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,CAACC,SAAQ;AACtD,cAAIH,UAAS,QAAQ,IAAIG,IAAG;AAC5B,iBAAOH,YAAW,UAAaA,QAAO,MAAM;AAAA,QAC7C,CAAC;AAED,iBAAS,CAAC,KAAKA,OAAM,KAAK,SAAS;AAClC,cAAIA,QAAO,MAAM,iBAAiB,EAAE,OAAO,SAAS;AACnD,qBAAS,KAAK,GAAG;AAAA,UAClB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAiB;AAChB,QAAE,sBAAsB;AAAA,MACzB;AAAA,IACD;AAAA,EAAC;AACF;AAMA,SAAS,UAAU,MAAM,MAAM;AAC9B,MAAI,OAAO,SAAS,SAAU,QAAO,GAAG,IAAI,WAAW,KAAK,eAAe,EAAE;AAC7E,MAAI,0BAA0B,KAAK,IAAI,EAAG,QAAO,GAAG,IAAI,IAAI,IAAI;AAChE,SAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI;AAClE;AAKO,SAAS,kBAAkB,OAAO;AACxC,MAAI;AACH,QAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,gBAAgB,OAAO;AACzE,aAAO,MAAM,YAAY;AAAA,IAC1B;AAAA,EACD,QAAQ;AAAA,EAQR;AAEA,SAAO;AACR;AAMO,SAAS,GAAG,GAAG,GAAG;AACxB,SAAO,OAAO,GAAG,kBAAkB,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAC5D;AAEA,IAAM,yBAAyB,oBAAI,IAAI;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAOD,SAAS,kBAAkB,OAAO;AACjC,SAAO,IAAI,MAAM,OAAO;AAAA,IACvB,IAAI,QAAQ,MAAM,UAAU;AAC3B,UAAI,QAAQ,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAC9C,UAAI,CAAC,uBAAuB;AAAA;AAAA,QAA2B;AAAA,MAAK,GAAG;AAC9D,eAAO;AAAA,MACR;AAMA,aAAO,YAAa,MAAM;AACzB,mCAA2B;AAC3B,YAAI,SAAS,MAAM,MAAM,MAAM,IAAI;AACnC,4BAAoB;AACpB,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD,CAAC;AACF;;;AC3aO,SAAS,gCAAgC;AAC/C,QAAMI,mBAAkB,MAAM;AAI9B,QAAM,UAAU,MAAM;AACtB,MAAI,SAAS;AACZ,YAAQ;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,aAAa,SAAS,IAAIA;AAE3C,EAAAA,iBAAgB,UAAU,SAAU,MAAM,YAAY;AACrD,UAAMC,SAAQ,QAAQ,KAAK,MAAM,MAAM,UAAU;AAEjD,QAAIA,WAAU,IAAI;AACjB,eAAS,IAAI,cAAc,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACtD,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,oBAAoB;AACpD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAOA;AAAA,EACR;AAEA,EAAAD,iBAAgB,cAAc,SAAU,MAAM,YAAY;AAGzD,UAAMC,SAAQ,YAAY,KAAK,MAAM,MAAM,cAAc,KAAK,SAAS,CAAC;AAExE,QAAIA,WAAU,IAAI;AACjB,eAAS,IAAI,GAAG,MAAM,cAAc,KAAK,SAAS,IAAI,KAAK,GAAG;AAC7D,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,wBAAwB;AACxD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAOA;AAAA,EACR;AAEA,EAAAD,iBAAgB,WAAW,SAAU,MAAM,YAAY;AACtD,UAAM,MAAM,SAAS,KAAK,MAAM,MAAM,UAAU;AAEhD,QAAI,CAAC,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACxC,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,qBAAqB;AACrD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAGA,QAAM,mBAAmB,MAAM;AAC9B,IAAAA,iBAAgB,UAAU;AAC1B,IAAAA,iBAAgB,cAAc;AAC9B,IAAAA,iBAAgB,WAAW;AAAA,EAC5B;AACD;AAQO,SAAS,cAAc,GAAG,GAAG,QAAQ,MAAM;AAGjD,MAAI;AACH,QAAK,MAAM,OAAQ,kBAAkB,CAAC,MAAM,kBAAkB,CAAC,IAAI;AAClE,MAAE,8BAA8B,QAAQ,QAAQ,KAAK;AAAA,IACtD;AAAA,EACD,QAAQ;AAAA,EAAC;AAET,SAAQ,MAAM,MAAO;AACtB;AAQO,SAASE,QAAO,GAAG,GAAG,QAAQ,MAAM;AAC1C,MAAK,KAAK,OAAQ,kBAAkB,CAAC,KAAK,kBAAkB,CAAC,IAAI;AAChE,IAAE,8BAA8B,QAAQ,OAAO,IAAI;AAAA,EACpD;AAEA,SAAQ,KAAK,MAAO;AACrB;;;ACxFO,IAAI;AAGJ,IAAI;AAGJ,IAAI;AAGX,IAAI;AAEJ,IAAI;AAMG,SAAS,kBAAkB;AACjC,MAAI,YAAY,QAAW;AAC1B;AAAA,EACD;AAEA,YAAU;AACV,cAAY;AACZ,eAAa,UAAU,KAAK,UAAU,SAAS;AAE/C,MAAI,oBAAoB,QAAQ;AAChC,MAAI,iBAAiB,KAAK;AAC1B,MAAI,iBAAiB,KAAK;AAG1B,uBAAqB,eAAe,gBAAgB,YAAY,EAAE;AAElE,wBAAsB,eAAe,gBAAgB,aAAa,EAAE;AAEpE,MAAI,cAAc,iBAAiB,GAAG;AAGrC,sBAAkB,UAAU;AAE5B,sBAAkB,cAAc;AAEhC,sBAAkB,eAAe;AAEjC,sBAAkB,UAAU;AAE5B,sBAAkB,MAAM;AAAA,EACzB;AAEA,MAAI,cAAc,cAAc,GAAG;AAElC,mBAAe,MAAM;AAAA,EACtB;AAEA,MAAI,cAAK;AAER,sBAAkB,gBAAgB;AAElC,kCAA8B;AAAA,EAC/B;AACD;AAMO,SAAS,YAAY,QAAQ,IAAI;AACvC,SAAO,SAAS,eAAe,KAAK;AACrC;AAQO,SAAS,gBAAgB,MAAM;AACrC,SAAO,mBAAmB,KAAK,IAAI;AACpC;AAQO,SAAS,iBAAiB,MAAM;AACtC,SAAO,oBAAoB,KAAK,IAAI;AACrC;AASO,SAAS,MAAM,MAAM,SAAS;AACpC,MAAI,CAAC,WAAW;AACf,WAAO,gBAAgB,IAAI;AAAA,EAC5B;AAEA,MAAIC;AAAA;AAAA,IAAqC,gBAAgB,YAAY;AAAA;AAGrE,MAAIA,WAAU,MAAM;AACnB,IAAAA,SAAQ,aAAa,YAAY,YAAY,CAAC;AAAA,EAC/C,WAAW,WAAWA,OAAM,aAAa,WAAW;AACnD,QAAI,OAAO,YAAY;AACvB,IAAAA,QAAO,OAAO,IAAI;AAClB,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiBA,MAAK;AACtB,SAAOA;AACR;AAQO,SAAS,YAAY,UAAU,UAAU,OAAO;AACtD,MAAI,CAAC,WAAW;AAEf,QAAI;AAAA;AAAA,MAAyC;AAAA;AAAA,QAAqC;AAAA,MAAS;AAAA;AAG3F,QAAI,iBAAiB,WAAW,MAAM,SAAS,GAAI,QAAO,iBAAiB,KAAK;AAEhF,WAAO;AAAA,EACR;AAIA,MAAI,WAAW,cAAc,aAAa,WAAW;AACpD,QAAI,OAAO,YAAY;AAEvB,kBAAc,OAAO,IAAI;AACzB,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AASO,SAAS,QAAQ,MAAM,QAAQ,GAAG,UAAU,OAAO;AACzD,MAAI,eAAe,YAAY,eAAe;AAC9C,MAAI;AAEJ,SAAO,SAAS;AACf,mBAAe;AACf;AAAA,IAA4C,iBAAiB,YAAY;AAAA,EAC1E;AAEA,MAAI,CAAC,WAAW;AACf,WAAO;AAAA,EACR;AAIA,MAAI,WAAW,cAAc,aAAa,WAAW;AACpD,QAAI,OAAO,YAAY;AAIvB,QAAI,iBAAiB,MAAM;AAC1B,oBAAc,MAAM,IAAI;AAAA,IACzB,OAAO;AACN,mBAAa,OAAO,IAAI;AAAA,IACzB;AACA,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACR;AAEA,mBAAiB,YAAY;AAC7B;AAAA;AAAA,IAAoC;AAAA;AACrC;AAOO,SAAS,mBAAmB,MAAM;AACxC,OAAK,cAAc;AACpB;AAQO,SAAS,sBAAsB;AACrC,MAAI,CAAC,gBAAiB,QAAO;AAC7B,MAAI,wBAAwB,KAAM,QAAO;AAEzC,MAAIC;AAAA;AAAA,IAA+B,cAAe;AAAA;AAClD,UAAQA,SAAQ,gBAAgB;AACjC;AASO,SAAS,eAAeC,MAAK,WAAWC,KAAI;AAClD,MAAI,UAAUA,MAAK,EAAE,IAAAA,IAAG,IAAI;AAC5B,MAAI,WAAW;AACd,WAAO,SAAS,gBAAgB,WAAWD,MAAK,OAAO;AAAA,EACxD;AACA,SAAO,SAAS,cAAcA,MAAK,OAAO;AAC3C;AAEO,SAAS,kBAAkB;AACjC,SAAO,SAAS,uBAAuB;AACxC;AAMO,SAAS,eAAe,OAAO,IAAI;AACzC,SAAO,SAAS,cAAc,IAAI;AACnC;AAQO,SAAS,cAAc,SAAS,KAAK,QAAQ,IAAI;AACvD,MAAI,IAAI,WAAW,QAAQ,GAAG;AAC7B,YAAQ,eAAe,gCAAgC,KAAK,KAAK;AACjE;AAAA,EACD;AACA,SAAO,QAAQ,aAAa,KAAK,KAAK;AACvC;;;ACtPO,IAAI,YAAY;AAGhB,SAAS,cAAc,OAAO;AACpC,cAAY;AACb;AASO,IAAI;AAGJ,SAAS,iBAAiB,MAAM;AACtC,MAAI,SAAS,MAAM;AAClB,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,SAAQ,eAAe;AACxB;AAEO,SAAS,eAAe;AAC9B,SAAO;AAAA;AAAA,IAA8C,iBAAiB,YAAY;AAAA,EAAE;AACrF;AAGO,SAAS,MAAM,MAAM;AAC3B,MAAI,CAAC,UAAW;AAGhB,MAAI,iBAAiB,YAAY,MAAM,MAAM;AAC5C,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,iBAAe;AAChB;AAKO,SAAS,iBAAiB,UAAU;AAC1C,MAAI,WAAW;AAEd,mBAAe,SAAS;AAAA,EACzB;AACD;AAEO,SAAS,KAAK,QAAQ,GAAG;AAC/B,MAAI,WAAW;AACd,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,KAAK;AACX;AAAA,MAAoC,iBAAiB,IAAI;AAAA,IAC1D;AAEA,mBAAe;AAAA,EAChB;AACD;AAMO,SAAS,WAAW,SAAS,MAAM;AACzC,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,MAAM;AACZ,QAAI,KAAK,aAAa,cAAc;AACnC,UAAI;AAAA;AAAA,QAA+B,KAAM;AAAA;AAEzC,UAAI,SAAS,eAAe;AAC3B,YAAI,UAAU,EAAG,QAAO;AACxB,iBAAS;AAAA,MACV,WAAW,SAAS,mBAAmB,SAAS,sBAAsB;AACrE,iBAAS;AAAA,MACV;AAAA,IACD;AAEA,QAAIE;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAC7D,QAAI,OAAQ,MAAK,OAAO;AACxB,WAAOA;AAAA,EACR;AACD;AAMO,SAAS,2BAA2B,MAAM;AAChD,MAAI,CAAC,QAAQ,KAAK,aAAa,cAAc;AAC5C,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA;AAAA;AAAA,IAA+B,KAAM;AAAA;AACtC;;;ACxGO,IAAM,wBAAwB,oBAAI,IAAI;AAGtC,IAAM,qBAAqB,oBAAI,IAAI;AAOnC,SAAS,cAAc,KAAK;AAClC,MAAI,CAAC,UAAW;AAEhB,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,gBAAgB,SAAS;AAE7B,QAAMC,SAAQ,IAAI;AAClB,MAAIA,WAAU,QAAW;AAExB,QAAI,MAAM;AACV,mBAAe,MAAM;AACpB,UAAI,IAAI,aAAa;AACpB,YAAI,cAAcA,MAAK;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAQO,SAAS,aAAa,YAAY,KAAK,SAAS,UAAU,CAAC,GAAG;AAIpE,WAAS,eAAoCA,QAAO;AACnD,QAAI,CAAC,QAAQ,SAAS;AAErB,+BAAyB,KAAK,KAAKA,MAAK;AAAA,IACzC;AACA,QAAI,CAACA,OAAM,cAAc;AACxB,aAAO,yBAAyB,MAAM;AACrC,eAAO,SAAS,KAAK,MAAMA,MAAK;AAAA,MACjC,CAAC;AAAA,IACF;AAAA,EACD;AAMA,MACC,WAAW,WAAW,SAAS,KAC/B,WAAW,WAAW,OAAO,KAC7B,eAAe,SACd;AACD,qBAAiB,MAAM;AACtB,UAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,IACzD,CAAC;AAAA,EACF,OAAO;AACN,QAAI,iBAAiB,YAAY,gBAAgB,OAAO;AAAA,EACzD;AAEA,SAAO;AACR;AAYO,SAAS,GAAG,SAAS,MAAM,SAAS,UAAU,CAAC,GAAG;AACxD,MAAI,iBAAiB,aAAa,MAAM,SAAS,SAAS,OAAO;AAEjE,SAAO,MAAM;AACZ,YAAQ,oBAAoB,MAAM,gBAAgB,OAAO;AAAA,EAC1D;AACD;AAUO,SAAS,MAAM,YAAY,KAAK,SAASC,UAAS,SAAS;AACjE,MAAI,UAAU,EAAE,SAAAA,UAAS,QAAQ;AACjC,MAAI,iBAAiB,aAAa,YAAY,KAAK,SAAS,OAAO;AAEnE,MACC,QAAQ,SAAS;AAAA,EAEjB,QAAQ;AAAA,EAER,QAAQ;AAAA,EAER,eAAe,kBACd;AACD,aAAS,MAAM;AACd,UAAI,oBAAoB,YAAY,gBAAgB,OAAO;AAAA,IAC5D,CAAC;AAAA,EACF;AACD;AAMO,SAAS,SAAS,QAAQ;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,0BAAsB,IAAI,OAAO,CAAC,CAAC;AAAA,EACpC;AAEA,WAAS,MAAM,oBAAoB;AAClC,OAAG,MAAM;AAAA,EACV;AACD;AAOA,IAAI,wBAAwB;AAOrB,SAAS,yBAAyBD,QAAO;AAC/C,MAAI,kBAAkB;AACtB,MAAI;AAAA;AAAA,IAAsC,gBAAiB;AAAA;AAC3D,MAAI,aAAaA,OAAM;AACvB,MAAI,OAAOA,OAAM,eAAe,KAAK,CAAC;AACtC,MAAI;AAAA;AAAA,IAAgD,KAAK,CAAC,KAAKA,OAAM;AAAA;AAErE,0BAAwBA;AAMxB,MAAI,WAAW;AAMf,MAAI,aAAa,0BAA0BA,UAASA,OAAM;AAE1D,MAAI,YAAY;AACf,QAAI,SAAS,KAAK,QAAQ,UAAU;AACpC,QACC,WAAW,OACV,oBAAoB,YAAY;AAAA,IAAwC,SACxE;AAKD,MAAAA,OAAM,SAAS;AACf;AAAA,IACD;AAOA,QAAI,cAAc,KAAK,QAAQ,eAAe;AAC9C,QAAI,gBAAgB,IAAI;AAGvB;AAAA,IACD;AAEA,QAAI,UAAU,aAAa;AAC1B,iBAAW;AAAA,IACZ;AAAA,EACD;AAEA;AAAA,EAAyC,KAAK,QAAQ,KAAKA,OAAM;AAIjE,MAAI,mBAAmB,gBAAiB;AAGxC,kBAAgBA,QAAO,iBAAiB;AAAA,IACvC,cAAc;AAAA,IACd,MAAM;AACL,aAAO,kBAAkB;AAAA,IAC1B;AAAA,EACD,CAAC;AAOD,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AAEtB,MAAI;AAIH,QAAI;AAIJ,QAAI,eAAe,CAAC;AAEpB,WAAO,mBAAmB,MAAM;AAE/B,UAAI,iBACH,eAAe,gBACf,eAAe;AAAA,MACK,eAAgB,QACpC;AAED,UAAI;AAEH,YAAI,YAAY,eAAe,OAAO,UAAU;AAEhD,YACC,aAAa,SACZ;AAAA,QAAsB,eAAgB;AAAA;AAAA,QAGtCA,OAAM,WAAW,iBACjB;AACD,oBAAU,KAAK,gBAAgBA,MAAK;AAAA,QACrC;AAAA,MACD,SAAS,OAAO;AACf,YAAI,aAAa;AAChB,uBAAa,KAAK,KAAK;AAAA,QACxB,OAAO;AACN,wBAAc;AAAA,QACf;AAAA,MACD;AACA,UAAIA,OAAM,gBAAgB,mBAAmB,mBAAmB,mBAAmB,MAAM;AACxF;AAAA,MACD;AACA,uBAAiB;AAAA,IAClB;AAEA,QAAI,aAAa;AAChB,eAAS,SAAS,cAAc;AAE/B,uBAAe,MAAM;AACpB,gBAAM;AAAA,QACP,CAAC;AAAA,MACF;AACA,YAAM;AAAA,IACP;AAAA,EACD,UAAE;AAED,IAAAA,OAAM,SAAS;AAEf,WAAOA,OAAM;AACb,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;AAYO,SAAS,MACf,OACA,SACA,MACA,WACA,KACA,mBAAmB,OACnB,gBAAgB,OACf;AACD,MAAI;AACJ,MAAI;AAEJ,MAAI;AACH,cAAU,MAAM;AAAA,EACjB,SAAS,GAAG;AACX,YAAQ;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,eAAe,oBAAoB,WAAW,QAAQ,QAAQ;AACpF,UAAM,WAAW,YAAY,QAAQ;AACrC,UAAM,WAAW,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,OAAO,QAAQ;AAC9E,UAAM,QAAQ,KAAK,CAAC,GAAG,aAAa,MAAM,iBAAiB,YAAY;AACvE,UAAM,aAAa,KAAK,CAAC,GAAG,OAAO;AACnC,UAAM,cAAc,KAAK,UAAU,aAAa,QAAQ;AACxD,UAAM,aAAa,gBAAgB,6BAA6B;AAEhE,IAAE,sBAAsB,aAAa,UAAU;AAE/C,QAAI,OAAO;AACV,YAAM;AAAA,IACP;AAAA,EACD;AACA,WAAS,MAAM,SAAS,IAAI;AAC7B;", "names": ["fallback", "stack", "flags", "component_context", "effect", "tag", "trace", "label", "stack", "source", "pending", "reset", "error", "boundary", "state", "i", "flags", "fallback", "effect", "key", "item", "next", "index", "run", "previous_batch", "boundary", "handle_error", "flags", "boundary", "d", "next", "derived", "stack", "source", "effect", "flags", "derived", "effect", "flags", "child", "source", "stack", "update", "e", "settled", "value", "eager_effects", "name", "event", "effect", "e", "derived", "flags", "teardown", "next", "child", "sibling", "effect", "flags", "index", "teardown", "trace", "derived", "get", "stack", "version", "source", "s", "value", "key", "array_prototype", "index", "equals", "child", "flags", "tag", "is", "next", "event", "capture"]}