{"compilerOptions": {"css": "external", "dev": true, "hmr": true}, "extensions": [".svelte"], "preprocess": {"name": "vite-preprocess", "script": "async script({ attributes, content, filename = '' }) {\n\t\t\tconst lang = /** @type {string} */ (attributes.lang);\n\t\t\tif (!supportedScriptLangs.includes(lang)) return;\n\t\t\tconst { code, map } = await transformWithEsbuild(content, filename, {\n\t\t\t\tloader: /** @type {import('vite').ESBuildOptions['loader']} */ (lang),\n\t\t\t\ttarget: 'esnext',\n\t\t\t\ttsconfigRaw: {\n\t\t\t\t\tcompilerOptions: {\n\t\t\t\t\t\t// svelte typescript needs this flag to work with type imports\n\t\t\t\t\t\timportsNotUsedAsValues: 'preserve',\n\t\t\t\t\t\tpreserveValueImports: true\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tmapToRelative(map, filename);\n\n\t\t\treturn {\n\t\t\t\tcode,\n\t\t\t\tmap\n\t\t\t};\n\t\t}", "style": "async ({ attributes, content, filename = '' }) => {\n\t\tconst ext = attributes.lang ? `.${attributes.lang}` : '.css';\n\t\tif (attributes.lang && !isCSSRequest(ext)) return;\n\t\tif (!cssTransform) {\n\t\t\tcssTransform = createCssTransform(style, config).then((t) => (cssTransform = t));\n\t\t}\n\t\tconst transform = await cssTransform;\n\t\tconst suffix = `${lang_sep}${ext}`;\n\t\tconst moduleId = `${filename}${suffix}`;\n\t\tconst { code, map, deps } = await transform(content, moduleId);\n\t\tremoveLangSuffix(map, suffix);\n\t\tmapToRelative(map, filename);\n\t\tconst dependencies = deps ? Array.from(deps).filter((d) => !d.endsWith(suffix)) : undefined;\n\t\treturn {\n\t\t\tcode,\n\t\t\tmap: map ?? undefined,\n\t\t\tdependencies\n\t\t};\n\t}"}}