{"version": 3, "sources": ["../../node_modules/svelte/src/internal/flags/index.js"], "sourcesContent": ["/** True if experimental.async=true */\nexport let async_mode_flag = false;\n/** True if we're not certain that we only have Svelte 5 code in the compilation */\nexport let legacy_mode_flag = false;\n/** True if $inspect.trace is used */\nexport let tracing_mode_flag = false;\n\nexport function enable_async_mode_flag() {\n\tasync_mode_flag = true;\n}\n\n/** ONLY USE THIS DURING TESTING */\nexport function disable_async_mode_flag() {\n\tasync_mode_flag = false;\n}\n\nexport function enable_legacy_mode_flag() {\n\tlegacy_mode_flag = true;\n}\n\nexport function enable_tracing_mode_flag() {\n\ttracing_mode_flag = true;\n}\n"], "mappings": ";AACO,IAAI,kBAAkB;AAEtB,IAAI,mBAAmB;AAEvB,IAAI,oBAAoB;AAExB,SAAS,yBAAyB;AACxC,oBAAkB;AACnB;AAOO,SAAS,0BAA0B;AACzC,qBAAmB;AACpB;AAEO,SAAS,2BAA2B;AAC1C,sBAAoB;AACrB;", "names": []}